import Logo from '@/components/ui/Logo';
import { Users, Award, Heart, Lightbulb, ArrowRight } from 'lucide-react';

export default function About() {
  return (
    <div className="min-h-screen bg-white text-black">
      {/* Apple-style Header */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-12">
            <a href="/">
              <Logo size="sm" />
            </a>
            <nav className="hidden md:flex items-center space-x-8">
              <a href="/" className="text-gray-600 hover:text-black transition-colors text-sm font-medium">Home</a>
              <a href="/about" className="text-black text-sm font-medium">About</a>
              <a href="/services" className="text-gray-600 hover:text-black transition-colors text-sm font-medium">Services</a>
              <a href="/portfolio" className="text-gray-600 hover:text-black transition-colors text-sm font-medium">Portfolio</a>
              <a href="/contact" className="text-gray-600 hover:text-black transition-colors text-sm font-medium">Contact</a>
            </nav>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-medium hover:bg-blue-700 transition-all">
              Get Quote
            </button>
          </div>
        </div>
      </header>

      {/* Sub Navigation */}
      <div className="fixed top-12 left-0 right-0 z-40 bg-white/90 backdrop-blur-md border-b border-gray-200/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center space-x-8 h-12 overflow-x-auto">
            <a href="#story" className="text-blue-600 text-sm font-medium whitespace-nowrap">Our Story</a>
            <a href="#values" className="text-gray-600 hover:text-black transition-colors text-sm font-medium whitespace-nowrap">Values</a>
            <a href="#team" className="text-gray-600 hover:text-black transition-colors text-sm font-medium whitespace-nowrap">Team</a>
            <a href="#awards" className="text-gray-600 hover:text-black transition-colors text-sm font-medium whitespace-nowrap">Awards</a>
            <a href="#careers" className="text-gray-600 hover:text-black transition-colors text-sm font-medium whitespace-nowrap">Careers</a>
            <a href="#contact" className="text-gray-600 hover:text-black transition-colors text-sm font-medium whitespace-nowrap">Contact</a>
          </div>
        </div>
      </div>

      <main className="pt-24">
        {/* Hero Section - Apple Style */}
        <section className="bg-black text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-6xl md:text-8xl font-bold mb-4 tracking-tight">
              About Us
            </h1>
            <p className="text-2xl md:text-3xl font-light mb-8 text-gray-300">
              Designed to inspire.
            </p>

            {/* Large Visual */}
            <div className="relative max-w-4xl mx-auto mb-12">
              <div className="aspect-video bg-gradient-to-br from-gray-800 to-gray-900 rounded-3xl flex items-center justify-center">
                <div className="text-center">
                  <Users className="w-24 h-24 mx-auto mb-4 text-gray-400" />
                  <p className="text-gray-400 text-lg">Crafting Visual Excellence</p>
                </div>
              </div>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 max-w-2xl mx-auto text-center">
              <div>
                <div className="text-3xl font-bold text-blue-400">10+</div>
                <div className="text-sm text-gray-400 uppercase tracking-wide">Years</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-blue-400">500+</div>
                <div className="text-sm text-gray-400 uppercase tracking-wide">Projects</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-blue-400">50+</div>
                <div className="text-sm text-gray-400 uppercase tracking-wide">Brands</div>
              </div>
            </div>
          </div>
        </section>

        {/* Our Story - Apple Style */}
        <section id="story" className="bg-gray-50 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              <div className="order-2 lg:order-1">
                <h2 className="text-5xl md:text-6xl font-bold mb-6 text-black">
                  Our Story
                </h2>
                <p className="text-xl text-gray-600 mb-8">
                  Founded with a passion for visual storytelling, we've grown into a leading force in luxury media production.
                </p>
                <div className="space-y-6 mb-8">
                  <p className="text-gray-700 leading-relaxed">
                    Our journey began with a simple belief: every brand has a unique story worth telling, and every story deserves to be told beautifully.
                  </p>
                  <p className="text-gray-700 leading-relaxed">
                    Over the years, we've had the privilege of working with some of the world's most prestigious brands, helping them communicate their vision through compelling visual narratives.
                  </p>
                </div>
                <button className="text-blue-600 font-medium flex items-center space-x-2 hover:underline">
                  <span>Learn more about our journey</span>
                  <ArrowRight className="w-4 h-4" />
                </button>
              </div>
              <div className="order-1 lg:order-2">
                <div className="aspect-square bg-gradient-to-br from-blue-100 to-blue-200 rounded-3xl flex items-center justify-center">
                  <Heart className="w-32 h-32 text-blue-600" />
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Our Values - Apple Style */}
        <section id="values" className="bg-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              <div>
                <div className="aspect-square bg-gradient-to-br from-purple-100 to-purple-200 rounded-3xl flex items-center justify-center">
                  <Lightbulb className="w-32 h-32 text-purple-600" />
                </div>
              </div>
              <div>
                <h2 className="text-5xl md:text-6xl font-bold mb-6 text-black">
                  Our Values
                </h2>
                <p className="text-xl text-gray-600 mb-8">
                  The principles that guide everything we do and define who we are as a creative partner.
                </p>
                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <div className="w-3 h-3 bg-purple-600 rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <h3 className="text-lg font-semibold text-black mb-2">Excellence</h3>
                      <p className="text-gray-700">We strive for perfection in every frame, every cut, and every creative decision.</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-4">
                    <div className="w-3 h-3 bg-purple-600 rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <h3 className="text-lg font-semibold text-black mb-2">Creativity</h3>
                      <p className="text-gray-700">Innovation and artistic vision drive our approach to every project we undertake.</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-4">
                    <div className="w-3 h-3 bg-purple-600 rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <h3 className="text-lg font-semibold text-black mb-2">Partnership</h3>
                      <p className="text-gray-700">We work closely with our clients as trusted collaborators, not just service providers.</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-4">
                    <div className="w-3 h-3 bg-purple-600 rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <h3 className="text-lg font-semibold text-black mb-2">Integrity</h3>
                      <p className="text-gray-700">Honesty, transparency, and ethical practices form the foundation of our business.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Team Section - Apple Style */}
        <section id="team" className="bg-gray-50 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-5xl md:text-6xl font-bold mb-6 text-black">
                Meet Our Team
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                A collective of passionate creatives, technical experts, and visionary storytellers.
              </p>
            </div>

            {/* Team Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
              {[
                { name: 'Sarah Johnson', role: 'Creative Director', desc: 'Visionary leader with 15+ years in luxury brand storytelling.', color: 'blue' },
                { name: 'Michael Chen', role: 'Lead Editor', desc: 'Award-winning editor specializing in cinematic post-production.', color: 'purple' },
                { name: 'Emma Rodriguez', role: 'Motion Graphics Artist', desc: 'Expert in creating stunning visual effects and animations.', color: 'green' },
                { name: 'David Kim', role: 'Color Grading Specialist', desc: 'Master of color science and visual mood creation.', color: 'orange' },
                { name: 'Lisa Thompson', role: 'Audio Engineer', desc: 'Specialist in immersive sound design and audio production.', color: 'pink' },
                { name: 'James Wilson', role: 'Project Manager', desc: 'Ensures seamless project delivery and client satisfaction.', color: 'indigo' }
              ].map((member, index) => {
                const colorClasses = {
                  blue: 'from-blue-100 to-blue-200 text-blue-600',
                  purple: 'from-purple-100 to-purple-200 text-purple-600',
                  green: 'from-green-100 to-green-200 text-green-600',
                  orange: 'from-orange-100 to-orange-200 text-orange-600',
                  pink: 'from-pink-100 to-pink-200 text-pink-600',
                  indigo: 'from-indigo-100 to-indigo-200 text-indigo-600'
                };
                return (
                  <div key={index} className="bg-white rounded-3xl p-8 shadow-lg text-center">
                    <div className={`w-20 h-20 bg-gradient-to-br ${colorClasses[member.color]} rounded-full mx-auto mb-4 flex items-center justify-center text-2xl font-bold`}>
                      {member.name.split(' ').map(n => n[0]).join('')}
                    </div>
                    <h3 className="text-xl font-semibold mb-2 text-black">{member.name}</h3>
                    <p className="text-gray-600 mb-3 font-medium">{member.role}</p>
                    <p className="text-gray-600 text-sm leading-relaxed">{member.desc}</p>
                  </div>
                );
              })}
            </div>

            {/* CTA */}
            <div className="text-center">
              <p className="text-gray-600 mb-4">Want to join our team?</p>
              <button className="text-blue-600 font-medium hover:underline">
                View open positions
              </button>
            </div>
          </div>
        </section>

        {/* Awards & Recognition - Apple Style */}
        <section id="awards" className="bg-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              <div className="order-2 lg:order-1">
                <h2 className="text-5xl md:text-6xl font-bold mb-6 text-black">
                  Awards & Recognition
                </h2>
                <p className="text-xl text-gray-600 mb-8">
                  Our commitment to excellence has been recognized by industry leaders and prestigious organizations.
                </p>
                <div className="space-y-6">
                  {[
                    { year: '2024', award: 'Best Commercial Production', org: 'Creative Excellence Awards' },
                    { year: '2023', award: 'Outstanding Visual Effects', org: 'Motion Graphics Society' },
                    { year: '2023', award: 'Luxury Brand Campaign', org: 'Marketing Excellence Awards' },
                    { year: '2022', award: 'Innovation in Post-Production', org: 'Industry Leaders Forum' }
                  ].map((award, index) => (
                    <div key={index} className="flex items-start space-x-4">
                      <div className="w-3 h-3 bg-green-600 rounded-full mt-2 flex-shrink-0"></div>
                      <div>
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="text-green-600 font-semibold">{award.year}</span>
                          <span className="text-gray-400">•</span>
                          <span className="text-gray-600 text-sm">{award.org}</span>
                        </div>
                        <h3 className="text-lg font-semibold text-black">{award.award}</h3>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              <div className="order-1 lg:order-2">
                <div className="aspect-square bg-gradient-to-br from-green-100 to-green-200 rounded-3xl flex items-center justify-center">
                  <Award className="w-32 h-32 text-green-600" />
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="bg-gray-50 py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-5xl md:text-6xl font-bold mb-6 text-black">
              Ready to work together?
            </h2>
            <p className="text-xl text-gray-600 mb-12">
              Let's create something extraordinary for your brand.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <button className="bg-blue-600 text-white px-8 py-3 rounded-full text-lg font-medium hover:bg-blue-700 transition-all">
                Start a project
              </button>
              <button className="text-blue-600 font-medium flex items-center space-x-2 hover:underline">
                <span>View our work</span>
                <ArrowRight className="w-4 h-4" />
              </button>
            </div>
          </div>
        </section>
      </main>

      {/* Apple-style Footer */}
      <footer className="bg-gray-100 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Footer Links */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-8 mb-8">
            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Services</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">Video Editing</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Motion Graphics</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Color Grading</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Audio Production</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Company</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">About</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Careers</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Press</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Blog</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Support</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">Contact Us</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Get Quote</a></li>
                <li><a href="#" className="hover:text-black transition-colors">FAQ</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Resources</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Portfolio</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">Commercial</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Corporate</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Music Videos</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Documentaries</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Connect</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">Instagram</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Twitter</a></li>
                <li><a href="#" className="hover:text-black transition-colors">LinkedIn</a></li>
                <li><a href="#" className="hover:text-black transition-colors">YouTube</a></li>
              </ul>
            </div>
          </div>

          {/* Footer Bottom */}
          <div className="border-t border-gray-300 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center text-sm text-gray-600">
              <p>Copyright © 2024 Jibreel Media Inc. All rights reserved.</p>
              <div className="flex space-x-6 mt-4 md:mt-0">
                <a href="#" className="hover:text-black transition-colors">Privacy Policy</a>
                <a href="#" className="hover:text-black transition-colors">Terms of Use</a>
                <a href="#" className="hover:text-black transition-colors">Legal</a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
