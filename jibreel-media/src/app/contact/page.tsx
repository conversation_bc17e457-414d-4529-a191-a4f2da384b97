import Logo from '@/components/ui/Logo';
import { Mail, Phone, MapPin, Calendar, MessageCircle, ArrowRight } from 'lucide-react';

export default function Contact() {
  return (
    <div className="min-h-screen bg-white text-black">
      {/* Apple-style Header */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-12">
            <a href="/">
              <Logo size="sm" />
            </a>
            <nav className="hidden md:flex items-center space-x-8">
              <a href="/" className="text-gray-600 hover:text-black transition-colors text-sm font-medium">Home</a>
              <a href="/about" className="text-gray-600 hover:text-black transition-colors text-sm font-medium">About</a>
              <a href="/services" className="text-gray-600 hover:text-black transition-colors text-sm font-medium">Services</a>
              <a href="/portfolio" className="text-gray-600 hover:text-black transition-colors text-sm font-medium">Portfolio</a>
              <a href="/contact" className="text-black text-sm font-medium">Contact</a>
            </nav>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-medium hover:bg-blue-700 transition-all">
              Get Quote
            </button>
          </div>
        </div>
      </header>

      {/* Sub Navigation */}
      <div className="fixed top-12 left-0 right-0 z-40 bg-white/90 backdrop-blur-md border-b border-gray-200/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center space-x-8 h-12 overflow-x-auto">
            <a href="#contact-form" className="text-blue-600 text-sm font-medium whitespace-nowrap">Contact Form</a>
            <a href="#contact-info" className="text-gray-600 hover:text-black transition-colors text-sm font-medium whitespace-nowrap">Contact Info</a>
            <a href="#faq" className="text-gray-600 hover:text-black transition-colors text-sm font-medium whitespace-nowrap">FAQ</a>
            <a href="#support" className="text-gray-600 hover:text-black transition-colors text-sm font-medium whitespace-nowrap">Support</a>
          </div>
        </div>
      </div>

      <main className="pt-24">
        {/* Hero Section - Apple Style */}
        <section className="bg-black text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-6xl md:text-8xl font-bold mb-4 tracking-tight">
              Contact
            </h1>
            <p className="text-2xl md:text-3xl font-light mb-8 text-gray-300">
              Designed to connect.
            </p>

            {/* Large Visual */}
            <div className="relative max-w-4xl mx-auto mb-12">
              <div className="aspect-video bg-gradient-to-br from-gray-800 to-gray-900 rounded-3xl flex items-center justify-center">
                <div className="text-center">
                  <MessageCircle className="w-24 h-24 mx-auto mb-4 text-gray-400" />
                  <p className="text-gray-400 text-lg">Let's Start a Conversation</p>
                </div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <button className="bg-blue-600 text-white px-8 py-3 rounded-full text-lg font-medium hover:bg-blue-700 transition-all">
                Start your project
              </button>
              <button className="border border-blue-600 text-blue-600 px-8 py-3 rounded-full text-lg font-medium hover:bg-blue-600 hover:text-white transition-all">
                Schedule consultation
              </button>
            </div>
          </div>
        </section>

        {/* Contact Form - Apple Style */}
        <section id="contact-form" className="bg-gray-50 py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-5xl md:text-6xl font-bold mb-6 text-black">
                Start Your Project
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Ready to elevate your brand with our premium services? Let's discuss your vision and create something extraordinary together.
              </p>
            </div>

            <div className="bg-white rounded-3xl p-8 shadow-lg">
              <form className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-black mb-2">
                      Full Name *
                    </label>
                    <input
                      type="text"
                      required
                      className="w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-xl text-black placeholder-gray-500 focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all"
                      placeholder="Your full name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-black mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      required
                      className="w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-xl text-black placeholder-gray-500 focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-black mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      className="w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-xl text-black placeholder-gray-500 focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all"
                      placeholder="+****************"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-black mb-2">
                      Company/Organization
                    </label>
                    <input
                      type="text"
                      className="w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-xl text-black placeholder-gray-500 focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all"
                      placeholder="Your company name"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-black mb-2">
                    Project Type *
                  </label>
                  <select
                    required
                    className="w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-xl text-black focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all"
                  >
                    <option value="">Select a service</option>
                    <option value="video-editing">Video Editing</option>
                    <option value="graphic-design">Graphic Design</option>
                    <option value="motion-graphics">Motion Graphics</option>
                    <option value="post-production">Post-Production</option>
                    <option value="audio-production">Audio Production</option>
                    <option value="creative-direction">Creative Direction</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-black mb-2">
                      Project Budget
                    </label>
                    <select className="w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-xl text-black focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all">
                      <option value="">Select budget range</option>
                      <option value="under-5k">Under $5,000</option>
                      <option value="5k-10k">$5,000 - $10,000</option>
                      <option value="10k-25k">$10,000 - $25,000</option>
                      <option value="25k-50k">$25,000 - $50,000</option>
                      <option value="50k-plus">$50,000+</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-black mb-2">
                      Project Timeline
                    </label>
                    <select className="w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-xl text-black focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all">
                      <option value="">Select timeline</option>
                      <option value="asap">ASAP</option>
                      <option value="1-month">Within 1 month</option>
                      <option value="2-3-months">2-3 months</option>
                      <option value="3-6-months">3-6 months</option>
                      <option value="flexible">Flexible</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-black mb-2">
                    Project Description *
                  </label>
                  <textarea
                    rows={6}
                    required
                    className="w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-xl text-black placeholder-gray-500 focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all resize-none"
                    placeholder="Tell us about your project, goals, and vision..."
                  ></textarea>
                </div>

                <button
                  type="submit"
                  className="w-full bg-blue-600 text-white px-8 py-4 rounded-full font-medium text-lg hover:bg-blue-700 transition-all"
                >
                  Send Message
                </button>
              </form>
            </div>
          </div>
        </section>

        {/* Contact Information - Apple Style */}
        <section id="contact-info" className="bg-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-5xl md:text-6xl font-bold mb-6 text-black">
                Contact Information
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Multiple ways to reach us and get the support you need.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="bg-gray-50 rounded-3xl p-8 text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Mail className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold mb-2 text-black">Email</h3>
                <p className="text-gray-600 mb-2"><EMAIL></p>
                <p className="text-gray-500 text-sm">We'll respond within 24 hours</p>
              </div>

              <div className="bg-gray-50 rounded-3xl p-8 text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Phone className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="text-xl font-bold mb-2 text-black">Phone</h3>
                <p className="text-gray-600 mb-2">+****************</p>
                <p className="text-gray-500 text-sm">Mon-Fri, 9AM-6PM EST</p>
              </div>

              <div className="bg-gray-50 rounded-3xl p-8 text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <MapPin className="w-8 h-8 text-purple-600" />
                </div>
                <h3 className="text-xl font-bold mb-2 text-black">Location</h3>
                <p className="text-gray-600 mb-2">New York, NY</p>
                <p className="text-gray-500 text-sm">Available for global projects</p>
              </div>

              <div className="bg-gray-50 rounded-3xl p-8 text-center">
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Calendar className="w-8 h-8 text-orange-600" />
                </div>
                <h3 className="text-xl font-bold mb-2 text-black">Schedule a Call</h3>
                <p className="text-gray-600 mb-4">Book a consultation</p>
                <button className="text-blue-600 font-medium hover:underline text-sm">
                  Schedule Now
                </button>
              </div>
            </div>

            {/* Social Links */}
            <div className="mt-16 text-center">
              <h3 className="text-2xl font-bold mb-8 text-black">Follow Us</h3>
              <div className="flex justify-center space-x-6">
                {[
                  { name: 'Instagram', icon: '📷', color: 'bg-pink-100 text-pink-600' },
                  { name: 'Twitter', icon: '🐦', color: 'bg-blue-100 text-blue-600' },
                  { name: 'LinkedIn', icon: '💼', color: 'bg-blue-100 text-blue-600' },
                  { name: 'YouTube', icon: '📺', color: 'bg-red-100 text-red-600' }
                ].map((social) => (
                  <a
                    key={social.name}
                    href="#"
                    className={`w-16 h-16 ${social.color} rounded-full flex items-center justify-center hover:scale-110 transition-transform text-2xl`}
                    title={social.name}
                  >
                    {social.icon}
                  </a>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section - Apple Style */}
        <section id="faq" className="bg-gray-50 py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-5xl md:text-6xl font-bold mb-6 text-black">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-gray-600">
                Everything you need to know about working with us.
              </p>
            </div>

            <div className="space-y-6">
              {[
                {
                  question: "What's your typical project timeline?",
                  answer: "Project timelines vary based on scope and complexity. Simple edits can be completed in 1-2 weeks, while comprehensive campaigns may take 4-8 weeks. We'll provide a detailed timeline during our initial consultation."
                },
                {
                  question: "Do you work with clients globally?",
                  answer: "Yes! We work with clients worldwide. Our team is experienced in remote collaboration and can accommodate different time zones for meetings and project coordination."
                },
                {
                  question: "What's included in your pricing?",
                  answer: "Our pricing includes all creative work, revisions (typically 2-3 rounds), project management, and final delivery in your preferred formats. Additional services like rush delivery or extensive revisions may incur extra costs."
                },
                {
                  question: "Can you handle large-scale projects?",
                  answer: "Absolutely. We've successfully delivered large-scale campaigns for Fortune 500 companies and luxury brands. Our team can scale to meet project demands while maintaining our quality standards."
                }
              ].map((faq, index) => (
                <div key={index} className="bg-white rounded-3xl p-8 shadow-lg">
                  <h3 className="text-xl font-bold mb-4 text-black">{faq.question}</h3>
                  <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Support Section */}
        <section id="support" className="bg-white py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-5xl md:text-6xl font-bold mb-6 text-black">
              Need more help?
            </h2>
            <p className="text-xl text-gray-600 mb-12">
              Our team is here to support you every step of the way.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-gray-50 rounded-3xl p-8">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <MessageCircle className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold mb-4 text-black">Live Chat</h3>
                <p className="text-gray-600 mb-6">Get instant answers to your questions</p>
                <button className="bg-blue-600 text-white px-6 py-3 rounded-full font-medium hover:bg-blue-700 transition-all">
                  Start Chat
                </button>
              </div>

              <div className="bg-gray-50 rounded-3xl p-8">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Phone className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="text-xl font-bold mb-4 text-black">Phone Support</h3>
                <p className="text-gray-600 mb-6">Speak directly with our team</p>
                <button className="bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-all">
                  Call Now
                </button>
              </div>

              <div className="bg-gray-50 rounded-3xl p-8">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Calendar className="w-8 h-8 text-purple-600" />
                </div>
                <h3 className="text-xl font-bold mb-4 text-black">Book a Meeting</h3>
                <p className="text-gray-600 mb-6">Schedule a personalized consultation</p>
                <button className="bg-purple-600 text-white px-6 py-3 rounded-full font-medium hover:bg-purple-700 transition-all">
                  Schedule
                </button>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Apple-style Footer */}
      <footer className="bg-gray-100 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Footer Links */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-8 mb-8">
            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Services</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">Video Editing</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Motion Graphics</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Color Grading</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Audio Production</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Portfolio</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">Commercial</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Corporate</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Music Videos</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Documentaries</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Support</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">Contact Us</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Get Quote</a></li>
                <li><a href="#" className="hover:text-black transition-colors">FAQ</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Resources</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Company</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">About</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Careers</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Press</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Blog</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Connect</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">Instagram</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Twitter</a></li>
                <li><a href="#" className="hover:text-black transition-colors">LinkedIn</a></li>
                <li><a href="#" className="hover:text-black transition-colors">YouTube</a></li>
              </ul>
            </div>
          </div>

          {/* Footer Bottom */}
          <div className="border-t border-gray-300 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center text-sm text-gray-600">
              <p>Copyright © 2024 Jibreel Media Inc. All rights reserved.</p>
              <div className="flex space-x-6 mt-4 md:mt-0">
                <a href="#" className="hover:text-black transition-colors">Privacy Policy</a>
                <a href="#" className="hover:text-black transition-colors">Terms of Use</a>
                <a href="#" className="hover:text-black transition-colors">Legal</a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
