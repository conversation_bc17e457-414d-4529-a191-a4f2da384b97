@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&family=Montserrat:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.cdnfonts.com/css/ethnocentric');
@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: #111111;
  color: #e5e5e5;
  font-family: 'Montserrat', system-ui, sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #2a2a2a;
}

::-webkit-scrollbar-thumb {
  background: #666666;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #888888;
}

/* Selection styling */
::selection {
  background: #666666;
  color: #ffffff;
}

/* Speed lines animation */
@keyframes speedLines {
  0% {
    transform: translateX(-100%) rotate(-5deg);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100vw) rotate(-5deg);
    opacity: 0;
  }
}

.speed-line {
  animation: speedLines 3s linear infinite;
}

/* Ethnocentric font class */
.font-ethnocentric {
  font-family: 'Ethnocentric', 'Impact', 'Arial Black', 'Helvetica', sans-serif;
  font-weight: 900;
  font-style: italic;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}
