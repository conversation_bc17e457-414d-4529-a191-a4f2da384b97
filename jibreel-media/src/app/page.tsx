'use client';

import { Film, Palette, Zap, <PERSON>tings, Headphones, Lightbulb, Play, ArrowRight, X } from 'lucide-react';
import Logo from '@/components/ui/Logo';
import { useState } from 'react';

export default function Home() {
  const [showBanner, setShowBanner] = useState(true);
  return (
    <div className="min-h-screen bg-white text-black">
      {/* Apple-style Header */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-12">
            <a href="/">
              <Logo size="sm" />
            </a>
            <nav className="hidden md:flex items-center space-x-8">
              <a href="/" className="text-black text-sm font-medium">Home</a>
              <a href="/about" className="text-gray-600 hover:text-black transition-colors text-sm font-medium">About</a>
              <a href="/services" className="text-gray-600 hover:text-black transition-colors text-sm font-medium">Services</a>
              <a href="/portfolio" className="text-gray-600 hover:text-black transition-colors text-sm font-medium">Portfolio</a>
              <a href="/contact" className="text-gray-600 hover:text-black transition-colors text-sm font-medium">Contact</a>
            </nav>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-medium hover:bg-blue-700 transition-all">
              Get Quote
            </button>
          </div>
        </div>
      </header>

      {/* Sub Navigation */}
      <div className="fixed top-12 left-0 right-0 z-40 bg-white/90 backdrop-blur-md border-b border-gray-200/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center space-x-8 h-12 overflow-x-auto">
            <a href="#" className="text-blue-600 text-sm font-medium whitespace-nowrap">Video Editing</a>
            <a href="#" className="text-gray-600 hover:text-black transition-colors text-sm font-medium whitespace-nowrap">Motion Graphics</a>
            <a href="#" className="text-gray-600 hover:text-black transition-colors text-sm font-medium whitespace-nowrap">Color Grading</a>
            <a href="#" className="text-gray-600 hover:text-black transition-colors text-sm font-medium whitespace-nowrap">Audio Production</a>
            <a href="#" className="text-gray-600 hover:text-black transition-colors text-sm font-medium whitespace-nowrap">Compare</a>
            <a href="#" className="text-gray-600 hover:text-black transition-colors text-sm font-medium whitespace-nowrap">Accessories</a>
            <a href="#" className="text-gray-600 hover:text-black transition-colors text-sm font-medium whitespace-nowrap">Shop Services</a>
          </div>
        </div>
      </div>

      <main className="pt-24">
        {/* Promotional Banner */}
        {showBanner && (
          <section className="bg-blue-600 text-white py-3">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
              <div className="text-center">
                <p className="text-sm">
                  Get credit toward professional video editing when you trade in an eligible project.
                  <a href="#" className="underline ml-1">Learn more</a>
                </p>
              </div>
              <button
                onClick={() => setShowBanner(false)}
                className="absolute right-0 top-1/2 transform -translate-y-1/2 p-1 hover:bg-blue-700 rounded-full transition-colors"
                aria-label="Close banner"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </section>
        )}

        {/* Hero Section - Apple Style */}
        <section className="bg-black text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-6xl md:text-8xl font-bold mb-4 tracking-tight">
              Jibreel Media
            </h1>
            <p className="text-2xl md:text-3xl font-light mb-8 text-gray-300">
              Designed to be loved.
            </p>

            {/* Large Product Image Placeholder */}
            <div className="relative max-w-4xl mx-auto mb-12">
              <div className="aspect-video bg-gradient-to-br from-gray-800 to-gray-900 rounded-3xl flex items-center justify-center">
                <div className="text-center">
                  <Film className="w-24 h-24 mx-auto mb-4 text-gray-400" />
                  <p className="text-gray-400 text-lg">Professional Video Production</p>
                </div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
              <button className="bg-blue-600 text-white px-8 py-3 rounded-full text-lg font-medium hover:bg-blue-700 transition-all">
                Learn more
              </button>
              <button className="border border-blue-600 text-blue-600 px-8 py-3 rounded-full text-lg font-medium hover:bg-blue-600 hover:text-white transition-all">
                Watch the film
              </button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 max-w-2xl mx-auto text-center">
              <div>
                <div className="text-3xl font-bold text-blue-400">500+</div>
                <div className="text-sm text-gray-400 uppercase tracking-wide">Projects</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-blue-400">50+</div>
                <div className="text-sm text-gray-400 uppercase tracking-wide">Brands</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-blue-400">10+</div>
                <div className="text-sm text-gray-400 uppercase tracking-wide">Years</div>
              </div>
            </div>
          </div>
        </section>

        {/* Product Showcase 1 - Video Editing */}
        <section className="bg-gray-50 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              <div className="order-2 lg:order-1">
                <h2 className="text-5xl md:text-6xl font-bold mb-6 text-black">
                  Video Editing Pro
                </h2>
                <p className="text-xl text-gray-600 mb-8">
                  Professional-grade editing with precision cuts, seamless transitions, and cinematic excellence.
                </p>
                <div className="space-y-4 mb-8">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <span className="text-gray-700">4K HDR color grading</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <span className="text-gray-700">Advanced motion graphics</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <span className="text-gray-700">Professional audio mixing</span>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row gap-4">
                  <button className="bg-blue-600 text-white px-6 py-3 rounded-full font-medium hover:bg-blue-700 transition-all">
                    Learn more
                  </button>
                  <button className="text-blue-600 font-medium flex items-center space-x-2 hover:underline">
                    <span>Watch showcase</span>
                    <ArrowRight className="w-4 h-4" />
                  </button>
                </div>
              </div>
              <div className="order-1 lg:order-2">
                <div className="aspect-square bg-gradient-to-br from-blue-100 to-blue-200 rounded-3xl flex items-center justify-center">
                  <Film className="w-32 h-32 text-blue-600" />
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Product Showcase 2 - Motion Graphics */}
        <section className="bg-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              <div>
                <div className="aspect-square bg-gradient-to-br from-purple-100 to-purple-200 rounded-3xl flex items-center justify-center">
                  <Zap className="w-32 h-32 text-purple-600" />
                </div>
              </div>
              <div>
                <h2 className="text-5xl md:text-6xl font-bold mb-6 text-black">
                  Motion Graphics
                </h2>
                <p className="text-xl text-gray-600 mb-8">
                  Dynamic animations and visual effects that captivate audiences and bring stories to life.
                </p>
                <div className="space-y-4 mb-8">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                    <span className="text-gray-700">3D animations</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                    <span className="text-gray-700">Visual effects</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                    <span className="text-gray-700">Brand animations</span>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row gap-4">
                  <button className="bg-purple-600 text-white px-6 py-3 rounded-full font-medium hover:bg-purple-700 transition-all">
                    Learn more
                  </button>
                  <button className="text-purple-600 font-medium flex items-center space-x-2 hover:underline">
                    <span>View portfolio</span>
                    <ArrowRight className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Apple-style CTA Section */}
        <section className="bg-gray-50 py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-5xl md:text-6xl font-bold mb-6 text-black">
              Which service is right for you?
            </h2>
            <p className="text-xl text-gray-600 mb-12">
              Compare our professional video production services.
            </p>

            {/* Service Comparison Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              <div className="bg-white rounded-3xl p-8 shadow-lg">
                <Film className="w-16 h-16 text-blue-600 mx-auto mb-4" />
                <h3 className="text-2xl font-bold mb-4 text-black">Video Editing</h3>
                <p className="text-gray-600 mb-6">Professional editing and post-production</p>
                <button className="bg-blue-600 text-white px-6 py-3 rounded-full font-medium hover:bg-blue-700 transition-all w-full">
                  Learn more
                </button>
              </div>

              <div className="bg-white rounded-3xl p-8 shadow-lg">
                <Zap className="w-16 h-16 text-purple-600 mx-auto mb-4" />
                <h3 className="text-2xl font-bold mb-4 text-black">Motion Graphics</h3>
                <p className="text-gray-600 mb-6">Dynamic animations and visual effects</p>
                <button className="bg-purple-600 text-white px-6 py-3 rounded-full font-medium hover:bg-purple-700 transition-all w-full">
                  Learn more
                </button>
              </div>

              <div className="bg-white rounded-3xl p-8 shadow-lg">
                <Settings className="w-16 h-16 text-green-600 mx-auto mb-4" />
                <h3 className="text-2xl font-bold mb-4 text-black">Full Production</h3>
                <p className="text-gray-600 mb-6">Complete end-to-end video production</p>
                <button className="bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-all w-full">
                  Learn more
                </button>
              </div>
            </div>

            <div className="text-center">
              <p className="text-gray-600 mb-4">Need help choosing?</p>
              <button className="text-blue-600 font-medium hover:underline">
                Chat with a Jibreel Media Specialist
              </button>
            </div>
          </div>
        </section>
      </main>

      {/* Apple-style Footer */}
      <footer className="bg-gray-100 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Footer Links */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-8 mb-8">
            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Services</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">Video Editing</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Motion Graphics</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Color Grading</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Audio Production</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Portfolio</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">Commercial</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Corporate</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Music Videos</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Documentaries</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Support</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">Contact Us</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Get Quote</a></li>
                <li><a href="#" className="hover:text-black transition-colors">FAQ</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Resources</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Company</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">About</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Careers</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Press</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Blog</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Connect</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">Instagram</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Twitter</a></li>
                <li><a href="#" className="hover:text-black transition-colors">LinkedIn</a></li>
                <li><a href="#" className="hover:text-black transition-colors">YouTube</a></li>
              </ul>
            </div>
          </div>

          {/* Footer Bottom */}
          <div className="border-t border-gray-300 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center text-sm text-gray-600">
              <p>Copyright © 2024 Jibreel Media Inc. All rights reserved.</p>
              <div className="flex space-x-6 mt-4 md:mt-0">
                <a href="#" className="hover:text-black transition-colors">Privacy Policy</a>
                <a href="#" className="hover:text-black transition-colors">Terms of Use</a>
                <a href="#" className="hover:text-black transition-colors">Sales Policy</a>
                <a href="#" className="hover:text-black transition-colors">Legal</a>
                <a href="#" className="hover:text-black transition-colors">Site Map</a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
