import Logo from '@/components/ui/Logo';
import { Film, Palette, Zap, Settings, Headphones, Lightbulb, ArrowRight, Play, Check } from 'lucide-react';

export default function Services() {
  const services = [
    {
      title: 'Video Editing Pro',
      subtitle: 'Cinematic Excellence',
      description: 'Transform raw footage into compelling visual narratives with precision cuts, seamless transitions, and expert pacing.',
      features: ['Professional Color Grading', 'Advanced Transitions', 'Narrative Flow Optimization', 'Multi-Camera Editing'],
      pricing: 'From $2,500',
      icon: Film,
      color: 'blue'
    },
    {
      title: 'Motion Graphics',
      subtitle: 'Dynamic Animations',
      description: 'Bring your ideas to life with dynamic animations, visual effects, and engaging motion graphics.',
      features: ['3D Animation', 'Visual Effects', 'Title Sequences', 'Logo Animation'],
      pricing: 'From $3,000',
      icon: Zap,
      color: 'purple'
    },
    {
      title: 'Graphic Design',
      subtitle: 'Visual Identity',
      description: 'Create stunning visual identities and marketing materials that reflect the sophistication of your brand.',
      features: ['Brand Identity Design', 'Marketing Collateral', 'Digital Assets', 'Print Design'],
      pricing: 'From $1,500',
      icon: Palette,
      color: 'green'
    },
    {
      title: 'Post-Production',
      subtitle: 'Complete Package',
      description: 'Full-service post-production from raw footage to final delivery, ensuring every detail meets luxury standards.',
      features: ['Sound Design', 'Audio Mixing', 'Final Mastering', 'Delivery Optimization'],
      pricing: 'From $4,000',
      icon: Settings,
      color: 'orange'
    },
    {
      title: 'Audio Production',
      subtitle: 'Crystal Clear Sound',
      description: 'Crystal-clear audio production with professional mixing, mastering, and immersive sound design.',
      features: ['Sound Mixing', 'Audio Enhancement', 'Music Production', 'Voice-over Recording'],
      pricing: 'From $1,200',
      icon: Headphones,
      color: 'pink'
    },
    {
      title: 'Creative Direction',
      subtitle: 'Strategic Guidance',
      description: 'Strategic creative guidance to develop and execute your vision with precision and artistic excellence.',
      features: ['Concept Development', 'Creative Strategy', 'Brand Storytelling', 'Project Planning'],
      pricing: 'From $2,000',
      icon: Lightbulb,
      color: 'indigo'
    }
  ];

  return (
    <div className="min-h-screen bg-white text-black">
      {/* Apple-style Header */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-12">
            <a href="/">
              <Logo size="sm" />
            </a>
            <nav className="hidden md:flex items-center space-x-8">
              <a href="/" className="text-gray-600 hover:text-black transition-colors text-sm font-medium">Home</a>
              <a href="/about" className="text-gray-600 hover:text-black transition-colors text-sm font-medium">About</a>
              <a href="/services" className="text-black text-sm font-medium">Services</a>
              <a href="/portfolio" className="text-gray-600 hover:text-black transition-colors text-sm font-medium">Portfolio</a>
              <a href="/contact" className="text-gray-600 hover:text-black transition-colors text-sm font-medium">Contact</a>
            </nav>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-medium hover:bg-blue-700 transition-all">
              Get Quote
            </button>
          </div>
        </div>
      </header>

      {/* Sub Navigation */}
      <div className="fixed top-12 left-0 right-0 z-40 bg-white/90 backdrop-blur-md border-b border-gray-200/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center space-x-8 h-12 overflow-x-auto">
            <a href="#video-editing" className="text-blue-600 text-sm font-medium whitespace-nowrap">Video Editing</a>
            <a href="#motion-graphics" className="text-gray-600 hover:text-black transition-colors text-sm font-medium whitespace-nowrap">Motion Graphics</a>
            <a href="#graphic-design" className="text-gray-600 hover:text-black transition-colors text-sm font-medium whitespace-nowrap">Graphic Design</a>
            <a href="#post-production" className="text-gray-600 hover:text-black transition-colors text-sm font-medium whitespace-nowrap">Post-Production</a>
            <a href="#audio" className="text-gray-600 hover:text-black transition-colors text-sm font-medium whitespace-nowrap">Audio</a>
            <a href="#creative-direction" className="text-gray-600 hover:text-black transition-colors text-sm font-medium whitespace-nowrap">Creative Direction</a>
          </div>
        </div>
      </div>

      <main className="pt-24">
        {/* Hero Section - Apple Style */}
        <section className="bg-black text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-6xl md:text-8xl font-bold mb-4 tracking-tight">
              Services
            </h1>
            <p className="text-2xl md:text-3xl font-light mb-8 text-gray-300">
              Designed for excellence.
            </p>

            {/* Large Visual */}
            <div className="relative max-w-4xl mx-auto mb-12">
              <div className="aspect-video bg-gradient-to-br from-gray-800 to-gray-900 rounded-3xl flex items-center justify-center">
                <div className="text-center">
                  <Play className="w-24 h-24 mx-auto mb-4 text-gray-400" />
                  <p className="text-gray-400 text-lg">Professional Media Production</p>
                </div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <button className="bg-blue-600 text-white px-8 py-3 rounded-full text-lg font-medium hover:bg-blue-700 transition-all">
                View all services
              </button>
              <button className="border border-blue-600 text-blue-600 px-8 py-3 rounded-full text-lg font-medium hover:bg-blue-600 hover:text-white transition-all">
                Get custom quote
              </button>
            </div>
          </div>
        </section>

        {/* Services Showcase - Apple Style */}
        {services.map((service, index) => {
          const IconComponent = service.icon;
          const isEven = index % 2 === 0;
          const colorClasses = {
            blue: { bg: 'from-blue-100 to-blue-200', text: 'text-blue-600', button: 'bg-blue-600 hover:bg-blue-700' },
            purple: { bg: 'from-purple-100 to-purple-200', text: 'text-purple-600', button: 'bg-purple-600 hover:bg-purple-700' },
            green: { bg: 'from-green-100 to-green-200', text: 'text-green-600', button: 'bg-green-600 hover:bg-green-700' },
            orange: { bg: 'from-orange-100 to-orange-200', text: 'text-orange-600', button: 'bg-orange-600 hover:bg-orange-700' },
            pink: { bg: 'from-pink-100 to-pink-200', text: 'text-pink-600', button: 'bg-pink-600 hover:bg-pink-700' },
            indigo: { bg: 'from-indigo-100 to-indigo-200', text: 'text-indigo-600', button: 'bg-indigo-600 hover:bg-indigo-700' }
          };
          const colors = colorClasses[service.color];

          return (
            <section key={index} id={service.title.toLowerCase().replace(/\s+/g, '-')} className={`${isEven ? 'bg-gray-50' : 'bg-white'} py-20`}>
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                  <div className={`${isEven ? 'order-2 lg:order-1' : 'order-1'}`}>
                    <h2 className="text-5xl md:text-6xl font-bold mb-2 text-black">
                      {service.title}
                    </h2>
                    <p className={`text-xl ${colors.text} mb-8 font-medium`}>
                      {service.subtitle}
                    </p>
                    <p className="text-xl text-gray-600 mb-8">
                      {service.description}
                    </p>
                    <div className="space-y-4 mb-8">
                      {service.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center space-x-3">
                          <Check className={`w-5 h-5 ${colors.text}`} />
                          <span className="text-gray-700">{feature}</span>
                        </div>
                      ))}
                    </div>
                    <div className="flex flex-col sm:flex-row gap-4 items-start">
                      <button className={`${colors.button} text-white px-6 py-3 rounded-full font-medium transition-all`}>
                        Learn more
                      </button>
                      <div className="flex flex-col">
                        <span className="text-gray-600 text-sm">Starting at</span>
                        <span className="text-2xl font-bold text-black">{service.pricing}</span>
                      </div>
                    </div>
                  </div>
                  <div className={`${isEven ? 'order-1 lg:order-2' : 'order-2'}`}>
                    <div className={`aspect-square bg-gradient-to-br ${colors.bg} rounded-3xl flex items-center justify-center`}>
                      <IconComponent className={`w-32 h-32 ${colors.text}`} />
                    </div>
                  </div>
                </div>
              </div>
            </section>
          );
        })}

        {/* Process Section - Apple Style */}
        <section className="bg-gray-50 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-5xl md:text-6xl font-bold mb-6 text-black">
                Our Process
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                A streamlined approach that ensures exceptional results and seamless collaboration.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                { step: '01', title: 'Discovery', desc: 'We begin by understanding your vision, goals, and brand requirements.', color: 'blue' },
                { step: '02', title: 'Strategy', desc: 'Develop a comprehensive creative strategy and project roadmap.', color: 'purple' },
                { step: '03', title: 'Production', desc: 'Execute the project with precision, creativity, and attention to detail.', color: 'green' },
                { step: '04', title: 'Delivery', desc: 'Final review, refinements, and delivery of your completed project.', color: 'orange' }
              ].map((process, index) => {
                const colorClasses = {
                  blue: 'from-blue-100 to-blue-200 text-blue-600',
                  purple: 'from-purple-100 to-purple-200 text-purple-600',
                  green: 'from-green-100 to-green-200 text-green-600',
                  orange: 'from-orange-100 to-orange-200 text-orange-600'
                };
                return (
                  <div key={index} className="text-center">
                    <div className={`w-16 h-16 bg-gradient-to-br ${colorClasses[process.color]} rounded-full mx-auto mb-4 flex items-center justify-center font-bold text-lg`}>
                      {process.step}
                    </div>
                    <h3 className="text-xl font-semibold mb-3 text-black">{process.title}</h3>
                    <p className="text-gray-600 leading-relaxed">{process.desc}</p>
                  </div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Service Comparison */}
        <section className="bg-white py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-5xl md:text-6xl font-bold mb-6 text-black">
              Which service is right for you?
            </h2>
            <p className="text-xl text-gray-600 mb-12">
              Compare our professional media production services.
            </p>

            {/* Service Comparison Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              <div className="bg-gray-50 rounded-3xl p-8 shadow-lg">
                <Film className="w-16 h-16 text-blue-600 mx-auto mb-4" />
                <h3 className="text-2xl font-bold mb-4 text-black">Video Editing</h3>
                <p className="text-gray-600 mb-6">Professional editing and post-production</p>
                <div className="text-2xl font-bold text-black mb-4">From $2,500</div>
                <button className="bg-blue-600 text-white px-6 py-3 rounded-full font-medium hover:bg-blue-700 transition-all w-full">
                  Learn more
                </button>
              </div>

              <div className="bg-gray-50 rounded-3xl p-8 shadow-lg">
                <Zap className="w-16 h-16 text-purple-600 mx-auto mb-4" />
                <h3 className="text-2xl font-bold mb-4 text-black">Motion Graphics</h3>
                <p className="text-gray-600 mb-6">Dynamic animations and visual effects</p>
                <div className="text-2xl font-bold text-black mb-4">From $3,000</div>
                <button className="bg-purple-600 text-white px-6 py-3 rounded-full font-medium hover:bg-purple-700 transition-all w-full">
                  Learn more
                </button>
              </div>

              <div className="bg-gray-50 rounded-3xl p-8 shadow-lg">
                <Settings className="w-16 h-16 text-green-600 mx-auto mb-4" />
                <h3 className="text-2xl font-bold mb-4 text-black">Full Production</h3>
                <p className="text-gray-600 mb-6">Complete end-to-end video production</p>
                <div className="text-2xl font-bold text-black mb-4">From $4,000</div>
                <button className="bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-all w-full">
                  Learn more
                </button>
              </div>
            </div>

            <div className="text-center">
              <p className="text-gray-600 mb-4">Need help choosing?</p>
              <button className="text-blue-600 font-medium hover:underline">
                Chat with a Jibreel Media Specialist
              </button>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="bg-gray-50 py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-5xl md:text-6xl font-bold mb-6 text-black">
              Ready to get started?
            </h2>
            <p className="text-xl text-gray-600 mb-12">
              Let's discuss your project and create something extraordinary together.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <button className="bg-blue-600 text-white px-8 py-3 rounded-full text-lg font-medium hover:bg-blue-700 transition-all">
                Request quote
              </button>
              <button className="text-blue-600 font-medium flex items-center space-x-2 hover:underline">
                <span>Schedule consultation</span>
                <ArrowRight className="w-4 h-4" />
              </button>
            </div>
          </div>
        </section>
      </main>

      {/* Apple-style Footer */}
      <footer className="bg-gray-100 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Footer Links */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-8 mb-8">
            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Services</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">Video Editing</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Motion Graphics</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Color Grading</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Audio Production</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Portfolio</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">Commercial</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Corporate</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Music Videos</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Documentaries</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Support</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">Contact Us</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Get Quote</a></li>
                <li><a href="#" className="hover:text-black transition-colors">FAQ</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Resources</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Company</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">About</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Careers</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Press</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Blog</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Connect</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">Instagram</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Twitter</a></li>
                <li><a href="#" className="hover:text-black transition-colors">LinkedIn</a></li>
                <li><a href="#" className="hover:text-black transition-colors">YouTube</a></li>
              </ul>
            </div>
          </div>

          {/* Footer Bottom */}
          <div className="border-t border-gray-300 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center text-sm text-gray-600">
              <p>Copyright © 2024 Jibreel Media Inc. All rights reserved.</p>
              <div className="flex space-x-6 mt-4 md:mt-0">
                <a href="#" className="hover:text-black transition-colors">Privacy Policy</a>
                <a href="#" className="hover:text-black transition-colors">Terms of Use</a>
                <a href="#" className="hover:text-black transition-colors">Legal</a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
