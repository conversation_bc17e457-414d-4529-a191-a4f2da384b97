'use client';

import { useState } from 'react';
import Logo from '@/components/ui/Logo';
import { Play, Award, Calendar, Tag, ArrowRight, Eye } from 'lucide-react';

export default function Portfolio() {
  const [activeFilter, setActiveFilter] = useState('All');

  const filters = ['All', 'Commercial', 'Documentary', 'Music Video', 'Corporate', 'Luxury'];

  const projects = [
    {
      id: 1,
      title: 'Luxury Fashion Campaign',
      client: 'Premium Fashion House',
      category: 'Commercial',
      description: 'A cinematic brand campaign showcasing elegance and sophistication through masterful editing and color grading.',
      year: '2024',
      awards: ['Best Commercial 2024', 'Creative Excellence Award'],
      tags: ['Fashion', 'Luxury', 'Cinematic'],
      color: 'blue',
      featured: true
    },
    {
      id: 2,
      title: 'Corporate Innovation Story',
      client: 'Fortune 500 Company',
      category: 'Corporate',
      description: 'An inspiring corporate documentary highlighting innovation and company culture.',
      year: '2024',
      awards: ['Documentary Excellence'],
      tags: ['Corporate', 'Innovation', 'Culture'],
      color: 'green',
      featured: false
    },
    {
      id: 3,
      title: 'Midnight Bloom',
      client: 'International Artist',
      category: 'Music Video',
      description: 'Dynamic visual storytelling synchronized with rhythm, featuring advanced motion graphics.',
      year: '2023',
      awards: ['Best Music Video', 'Visual Effects Award'],
      tags: ['Music', 'Effects', 'Artistic'],
      color: 'purple',
      featured: true
    },
    {
      id: 4,
      title: 'Heritage Collection',
      client: 'Luxury Watch Brand',
      category: 'Luxury',
      description: 'Elegant product showcase emphasizing craftsmanship and timeless design.',
      year: '2023',
      awards: ['Luxury Marketing Award'],
      tags: ['Product', 'Luxury', 'Craftsmanship'],
      color: 'orange',
      featured: false
    },
    {
      id: 5,
      title: 'Urban Echoes',
      client: 'Independent Film',
      category: 'Documentary',
      description: 'A compelling narrative exploring urban landscapes and hidden stories.',
      year: '2023',
      awards: ['Documentary Film Festival Winner'],
      tags: ['Urban', 'Storytelling', 'Culture'],
      color: 'indigo',
      featured: true
    },
    {
      id: 6,
      title: 'Tech Summit Highlights',
      client: 'Technology Conference',
      category: 'Corporate',
      description: 'Fast-paced event coverage capturing key moments and insights.',
      year: '2024',
      awards: [],
      tags: ['Event', 'Technology', 'Conference'],
      color: 'pink',
      featured: false
    }
  ];

  const filteredProjects = activeFilter === 'All'
    ? projects
    : projects.filter(project => project.category === activeFilter);

  return (
    <div className="min-h-screen bg-white text-black">
      {/* Apple-style Header */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-12">
            <a href="/">
              <Logo size="sm" />
            </a>
            <nav className="hidden md:flex items-center space-x-8">
              <a href="/" className="text-gray-600 hover:text-black transition-colors text-sm font-medium">Home</a>
              <a href="/about" className="text-gray-600 hover:text-black transition-colors text-sm font-medium">About</a>
              <a href="/services" className="text-gray-600 hover:text-black transition-colors text-sm font-medium">Services</a>
              <a href="/portfolio" className="text-black text-sm font-medium">Portfolio</a>
              <a href="/contact" className="text-gray-600 hover:text-black transition-colors text-sm font-medium">Contact</a>
            </nav>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-medium hover:bg-blue-700 transition-all">
              Get Quote
            </button>
          </div>
        </div>
      </header>



      {/* Sub Navigation */}
      <div className="fixed top-12 left-0 right-0 z-40 bg-white/90 backdrop-blur-md border-b border-gray-200/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center space-x-8 h-12 overflow-x-auto">
            {filters.map((filter) => (
              <button
                key={filter}
                onClick={() => setActiveFilter(filter)}
                className={`text-sm font-medium whitespace-nowrap transition-colors ${
                  activeFilter === filter
                    ? 'text-blue-600'
                    : 'text-gray-600 hover:text-black'
                }`}
              >
                {filter}
              </button>
            ))}
          </div>
        </div>
      </div>

      <main className="pt-24">
        {/* Hero Section - Apple Style */}
        <section className="bg-black text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-6xl md:text-8xl font-bold mb-4 tracking-tight">
              Portfolio
            </h1>
            <p className="text-2xl md:text-3xl font-light mb-8 text-gray-300">
              Designed to inspire.
            </p>

            {/* Large Visual */}
            <div className="relative max-w-4xl mx-auto mb-12">
              <div className="aspect-video bg-gradient-to-br from-gray-800 to-gray-900 rounded-3xl flex items-center justify-center">
                <div className="text-center">
                  <Eye className="w-24 h-24 mx-auto mb-4 text-gray-400" />
                  <p className="text-gray-400 text-lg">Award-Winning Projects</p>
                </div>
              </div>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 max-w-2xl mx-auto text-center">
              <div>
                <div className="text-3xl font-bold text-blue-400">50+</div>
                <div className="text-sm text-gray-400 uppercase tracking-wide">Projects</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-blue-400">25+</div>
                <div className="text-sm text-gray-400 uppercase tracking-wide">Awards</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-blue-400">100%</div>
                <div className="text-sm text-gray-400 uppercase tracking-wide">Satisfaction</div>
              </div>
            </div>
          </div>
        </section>



        {/* Featured Projects - Apple Style */}
        {filteredProjects.filter(project => project.featured).map((project, index) => {
          const isEven = index % 2 === 0;
          const colorClasses = {
            blue: { bg: 'from-blue-100 to-blue-200', text: 'text-blue-600', button: 'bg-blue-600 hover:bg-blue-700' },
            purple: { bg: 'from-purple-100 to-purple-200', text: 'text-purple-600', button: 'bg-purple-600 hover:bg-purple-700' },
            green: { bg: 'from-green-100 to-green-200', text: 'text-green-600', button: 'bg-green-600 hover:bg-green-700' },
            orange: { bg: 'from-orange-100 to-orange-200', text: 'text-orange-600', button: 'bg-orange-600 hover:bg-orange-700' },
            pink: { bg: 'from-pink-100 to-pink-200', text: 'text-pink-600', button: 'bg-pink-600 hover:bg-pink-700' },
            indigo: { bg: 'from-indigo-100 to-indigo-200', text: 'text-indigo-600', button: 'bg-indigo-600 hover:bg-indigo-700' }
          };
          const colors = colorClasses[project.color];

          return (
            <section key={project.id} className={`${isEven ? 'bg-gray-50' : 'bg-white'} py-20`}>
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                  <div className={`${isEven ? 'order-2 lg:order-1' : 'order-1'}`}>
                    <div className="flex items-center space-x-2 mb-4">
                      <span className={`px-3 py-1 ${colors.button} text-white text-sm font-medium rounded-full`}>
                        {project.category}
                      </span>
                      <span className="text-gray-600 text-sm">{project.year}</span>
                    </div>
                    <h2 className="text-5xl md:text-6xl font-bold mb-2 text-black">
                      {project.title}
                    </h2>
                    <p className={`text-xl ${colors.text} mb-6 font-medium`}>
                      {project.client}
                    </p>
                    <p className="text-xl text-gray-600 mb-8">
                      {project.description}
                    </p>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-4 mb-8">
                      {project.tags.map((tag, tagIndex) => (
                        <span key={tagIndex} className="flex items-center space-x-2 text-gray-700">
                          <Tag className="w-4 h-4" />
                          <span>{tag}</span>
                        </span>
                      ))}
                    </div>

                    {/* Awards */}
                    {project.awards.length > 0 && (
                      <div className="mb-8">
                        <h4 className="text-lg font-semibold text-black mb-4 flex items-center space-x-2">
                          <Award className={`w-5 h-5 ${colors.text}`} />
                          <span>Awards</span>
                        </h4>
                        <div className="space-y-2">
                          {project.awards.map((award, awardIndex) => (
                            <div key={awardIndex} className="flex items-center space-x-3">
                              <div className={`w-2 h-2 ${colors.button} rounded-full`}></div>
                              <span className="text-gray-700">{award}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    <div className="flex flex-col sm:flex-row gap-4">
                      <button className={`${colors.button} text-white px-6 py-3 rounded-full font-medium transition-all flex items-center space-x-2`}>
                        <Play className="w-4 h-4" />
                        <span>Watch project</span>
                      </button>
                      <button className="text-gray-600 font-medium flex items-center space-x-2 hover:underline">
                        <span>View case study</span>
                        <ArrowRight className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                  <div className={`${isEven ? 'order-1 lg:order-2' : 'order-2'}`}>
                    <div className={`aspect-video bg-gradient-to-br ${colors.bg} rounded-3xl flex items-center justify-center relative overflow-hidden`}>
                      <div className="text-center">
                        <Play className={`w-24 h-24 ${colors.text} mb-4`} />
                        <p className={`${colors.text} text-lg font-medium`}>Project Preview</p>
                      </div>
                      {/* Play button overlay */}
                      <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-black/20 rounded-3xl">
                        <button className="w-16 h-16 bg-white/90 rounded-full flex items-center justify-center">
                          <Play className="w-6 h-6 text-black ml-1" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          );
        })}

        {/* All Projects Grid */}
        <section className="bg-gray-50 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-5xl md:text-6xl font-bold mb-6 text-black">
                All Projects
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Explore our complete portfolio of award-winning projects.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredProjects.map((project) => {
                const colorClasses = {
                  blue: 'from-blue-100 to-blue-200 text-blue-600',
                  purple: 'from-purple-100 to-purple-200 text-purple-600',
                  green: 'from-green-100 to-green-200 text-green-600',
                  orange: 'from-orange-100 to-orange-200 text-orange-600',
                  pink: 'from-pink-100 to-pink-200 text-pink-600',
                  indigo: 'from-indigo-100 to-indigo-200 text-indigo-600'
                };
                const colors = colorClasses[project.color];

                return (
                  <div key={project.id} className="bg-white rounded-3xl p-6 shadow-lg hover:shadow-xl transition-shadow group cursor-pointer">
                    {/* Project Thumbnail */}
                    <div className={`aspect-video bg-gradient-to-br ${colors} rounded-2xl mb-6 flex items-center justify-center relative overflow-hidden`}>
                      <Play className="w-12 h-12" />
                      {/* Hover overlay */}
                      <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                        <button className="w-12 h-12 bg-white/90 rounded-full flex items-center justify-center">
                          <Play className="w-4 h-4 text-black ml-0.5" />
                        </button>
                      </div>
                    </div>

                    {/* Project Info */}
                    <div className="flex items-center justify-between mb-3">
                      <span className="px-3 py-1 bg-gray-100 text-gray-700 text-sm font-medium rounded-full">
                        {project.category}
                      </span>
                      <div className="flex items-center space-x-1 text-gray-500">
                        <Calendar className="w-4 h-4" />
                        <span className="text-sm">{project.year}</span>
                      </div>
                    </div>

                    <h3 className="text-xl font-bold mb-2 text-black group-hover:text-blue-600 transition-colors">
                      {project.title}
                    </h3>
                    <p className="text-gray-600 text-sm mb-4">{project.client}</p>
                    <p className="text-gray-700 text-sm mb-4 leading-relaxed">
                      {project.description}
                    </p>

                    {/* Awards */}
                    {project.awards.length > 0 && (
                      <div className="border-t border-gray-200 pt-4">
                        <div className="flex items-center space-x-2 mb-2">
                          <Award className="w-4 h-4 text-yellow-500" />
                          <span className="text-sm font-medium text-gray-700">Awards</span>
                        </div>
                        <div className="space-y-1">
                          {project.awards.slice(0, 2).map((award, awardIndex) => (
                            <div key={awardIndex} className="text-xs text-gray-600">
                              {award}
                            </div>
                          ))}
                          {project.awards.length > 2 && (
                            <div className="text-xs text-blue-600">
                              +{project.awards.length - 2} more
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>

            {filteredProjects.length === 0 && (
              <div className="text-center py-16">
                <p className="text-gray-600 text-lg">No projects found for the selected category.</p>
              </div>
            )}
          </div>
        </section>

        {/* CTA Section */}
        <section className="bg-white py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-5xl md:text-6xl font-bold mb-6 text-black">
              Ready to create your masterpiece?
            </h2>
            <p className="text-xl text-gray-600 mb-12">
              Let's collaborate to bring your vision to life with the same excellence and creativity.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <button className="bg-blue-600 text-white px-8 py-3 rounded-full text-lg font-medium hover:bg-blue-700 transition-all">
                Start your project
              </button>
              <button className="text-blue-600 font-medium flex items-center space-x-2 hover:underline">
                <span>View our process</span>
                <ArrowRight className="w-4 h-4" />
              </button>
            </div>
          </div>
        </section>
      </main>

      {/* Apple-style Footer */}
      <footer className="bg-gray-100 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Footer Links */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-8 mb-8">
            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Services</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">Video Editing</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Motion Graphics</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Color Grading</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Audio Production</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Portfolio</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">Commercial</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Corporate</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Music Videos</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Documentaries</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Support</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">Contact Us</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Get Quote</a></li>
                <li><a href="#" className="hover:text-black transition-colors">FAQ</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Resources</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Company</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">About</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Careers</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Press</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Blog</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-black mb-4">Connect</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-black transition-colors">Instagram</a></li>
                <li><a href="#" className="hover:text-black transition-colors">Twitter</a></li>
                <li><a href="#" className="hover:text-black transition-colors">LinkedIn</a></li>
                <li><a href="#" className="hover:text-black transition-colors">YouTube</a></li>
              </ul>
            </div>
          </div>

          {/* Footer Bottom */}
          <div className="border-t border-gray-300 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center text-sm text-gray-600">
              <p>Copyright © 2024 Jibreel Media Inc. All rights reserved.</p>
              <div className="flex space-x-6 mt-4 md:mt-0">
                <a href="#" className="hover:text-black transition-colors">Privacy Policy</a>
                <a href="#" className="hover:text-black transition-colors">Terms of Use</a>
                <a href="#" className="hover:text-black transition-colors">Legal</a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
