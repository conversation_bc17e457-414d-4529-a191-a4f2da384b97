{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/GitHub/Jibreel_Media/jibreel-media/src/components/ui/Logo.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Image from 'next/image';\nimport { Video } from 'lucide-react';\n\ninterface LogoProps {\n  className?: string;\n  size?: 'sm' | 'md' | 'lg';\n  showIcon?: boolean;\n  textOnly?: boolean;\n}\n\nconst Logo: React.FC<LogoProps> = ({\n  className = '',\n  size = 'md',\n  showIcon = true,\n  textOnly = false\n}) => {\n  const [imageError, setImageError] = useState(false);\n\n  const sizeClasses = {\n    sm: 'text-lg',\n    md: 'text-2xl',\n    lg: 'text-4xl'\n  };\n\n  const logoSizes = {\n    sm: { width: 120, height: 40 },\n    md: { width: 180, height: 60 },\n    lg: { width: 240, height: 80 }\n  };\n\n  const iconSizes = {\n    sm: 'w-5 h-5',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  };\n\n  // If textOnly is true, just show the text\n  if (textOnly) {\n    return (\n      <div className={`font-ethnocentric ${sizeClasses[size]} bg-gradient-to-r from-gray-300 to-white bg-clip-text text-transparent tracking-wider ${className}`}>\n        JIBREEL MEDIA\n      </div>\n    );\n  }\n\n  // Try to use the logo image first, fallback to icon + text\n  return (\n    <div className={`flex items-center ${className}`}>\n      {!imageError ? (\n        <Image\n          src=\"/images/jibreel-logo.svg\"\n          alt=\"Jibreel Media\"\n          width={logoSizes[size].width}\n          height={logoSizes[size].height}\n          className=\"object-contain\"\n          onError={() => setImageError(true)}\n          priority\n        />\n      ) : (\n        // Fallback to icon + text if image fails to load\n        <div className=\"flex items-center space-x-3\">\n          {showIcon && (\n            <div className=\"relative\">\n              {/* Film camera icon - more detailed */}\n              <div className=\"relative bg-black rounded-lg p-1\">\n                <Video className={`${iconSizes[size]} text-white`} />\n                {/* Film reels on top */}\n                <div className=\"absolute -top-2 left-1\">\n                  <div className=\"w-4 h-4 bg-gray-800 rounded-full border-2 border-gray-600 flex items-center justify-center\">\n                    <div className=\"w-1 h-1 bg-gray-400 rounded-full\"></div>\n                  </div>\n                </div>\n                <div className=\"absolute -top-2 right-1\">\n                  <div className=\"w-4 h-4 bg-gray-800 rounded-full border-2 border-gray-600 flex items-center justify-center\">\n                    <div className=\"w-1 h-1 bg-gray-400 rounded-full\"></div>\n                  </div>\n                </div>\n                {/* Lens */}\n                <div className=\"absolute -right-2 top-1/2 transform -translate-y-1/2\">\n                  <div className=\"w-3 h-3 bg-gray-700 rounded-full border border-gray-500\"></div>\n                </div>\n              </div>\n            </div>\n          )}\n          <div className={`font-ethnocentric ${sizeClasses[size]} bg-gradient-to-r from-gray-300 to-white bg-clip-text text-transparent tracking-wider`}>\n            JIBREEL MEDIA\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Logo;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAaA,MAAM,OAA4B;QAAC,EACjC,YAAY,EAAE,EACd,OAAO,IAAI,EACX,WAAW,IAAI,EACf,WAAW,KAAK,EACjB;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,YAAY;QAChB,IAAI;YAAE,OAAO;YAAK,QAAQ;QAAG;QAC7B,IAAI;YAAE,OAAO;YAAK,QAAQ;QAAG;QAC7B,IAAI;YAAE,OAAO;YAAK,QAAQ;QAAG;IAC/B;IAEA,MAAM,YAAY;QAChB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,0CAA0C;IAC1C,IAAI,UAAU;QACZ,qBACE,6LAAC;YAAI,WAAW,AAAC,qBAA8H,OAA1G,WAAW,CAAC,KAAK,EAAC,0FAAkG,OAAV;sBAAa;;;;;;IAIhK;IAEA,2DAA2D;IAC3D,qBACE,6LAAC;QAAI,WAAW,AAAC,qBAA8B,OAAV;kBAClC,CAAC,2BACA,6LAAC,gIAAA,CAAA,UAAK;YACJ,KAAI;YACJ,KAAI;YACJ,OAAO,SAAS,CAAC,KAAK,CAAC,KAAK;YAC5B,QAAQ,SAAS,CAAC,KAAK,CAAC,MAAM;YAC9B,WAAU;YACV,SAAS,IAAM,cAAc;YAC7B,QAAQ;;;;;uDAGV,iDAAiD;sBACjD,6LAAC;YAAI,WAAU;;gBACZ,0BACC,6LAAC;oBAAI,WAAU;8BAEb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAW,AAAC,GAAkB,OAAhB,SAAS,CAAC,KAAK,EAAC;;;;;;0CAErC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;0CAGnB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;0CAInB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;8BAKvB,6LAAC;oBAAI,WAAW,AAAC,qBAAsC,OAAlB,WAAW,CAAC,KAAK,EAAC;8BAAwF;;;;;;;;;;;;;;;;;AAOzJ;GAjFM;KAAA;uCAmFS", "debugId": null}}]}