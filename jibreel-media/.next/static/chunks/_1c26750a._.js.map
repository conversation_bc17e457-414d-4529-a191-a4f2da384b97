{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/GitHub/Jibreel_Media/jibreel-media/src/app/portfolio/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\nexport default function Portfolio() {\n  const [activeFilter, setActiveFilter] = useState('All');\n\n  const filters = ['All', 'Commercial', 'Documentary', 'Music Video', 'Corporate', 'Luxury'];\n\n  const projects = [\n    {\n      id: 1,\n      title: 'Luxury Fashion Campaign',\n      client: 'Premium Fashion House',\n      category: 'Commercial',\n      description: 'A cinematic brand campaign showcasing elegance and sophistication through masterful editing and color grading.',\n      year: '2024',\n      awards: ['Best Commercial 2024', 'Creative Excellence Award'],\n      tags: ['Fashion', 'Luxury', 'Cinematic']\n    },\n    {\n      id: 2,\n      title: 'Corporate Innovation Story',\n      client: 'Fortune 500 Company',\n      category: 'Corporate',\n      description: 'An inspiring corporate documentary highlighting innovation and company culture.',\n      year: '2024',\n      awards: ['Documentary Excellence'],\n      tags: ['Corporate', 'Innovation', 'Culture']\n    },\n    {\n      id: 3,\n      title: 'Midnight Bloom',\n      client: 'International Artist',\n      category: 'Music Video',\n      description: 'Dynamic visual storytelling synchronized with rhythm, featuring advanced motion graphics.',\n      year: '2023',\n      awards: ['Best Music Video', 'Visual Effects Award'],\n      tags: ['Music', 'Effects', 'Artistic']\n    },\n    {\n      id: 4,\n      title: 'Heritage Collection',\n      client: 'Luxury Watch Brand',\n      category: 'Luxury',\n      description: 'Elegant product showcase emphasizing craftsmanship and timeless design.',\n      year: '2023',\n      awards: ['Luxury Marketing Award'],\n      tags: ['Product', 'Luxury', 'Craftsmanship']\n    },\n    {\n      id: 5,\n      title: 'Urban Echoes',\n      client: 'Independent Film',\n      category: 'Documentary',\n      description: 'A compelling narrative exploring urban landscapes and hidden stories.',\n      year: '2023',\n      awards: ['Documentary Film Festival Winner'],\n      tags: ['Urban', 'Storytelling', 'Culture']\n    },\n    {\n      id: 6,\n      title: 'Tech Summit Highlights',\n      client: 'Technology Conference',\n      category: 'Corporate',\n      description: 'Fast-paced event coverage capturing key moments and insights.',\n      year: '2024',\n      awards: [],\n      tags: ['Event', 'Technology', 'Conference']\n    }\n  ];\n\n  const filteredProjects = activeFilter === 'All' \n    ? projects \n    : projects.filter(project => project.category === activeFilter);\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 text-gray-100\">\n      {/* Header */}\n      <header className=\"fixed top-0 left-0 right-0 z-50 bg-gray-900/95 backdrop-blur-md border-b border-gray-600/20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-20\">\n            <a href=\"/\" className=\"text-2xl font-bold bg-gradient-to-r from-gray-300 to-white bg-clip-text text-transparent\">\n              Jibreel Media\n            </a>\n            <nav className=\"hidden md:flex items-center space-x-8\">\n              <a href=\"/\" className=\"text-gray-300 hover:text-gray-100 transition-colors\">Home</a>\n              <a href=\"/about\" className=\"text-gray-300 hover:text-gray-100 transition-colors\">About</a>\n              <a href=\"/services\" className=\"text-gray-300 hover:text-gray-100 transition-colors\">Services</a>\n              <a href=\"/portfolio\" className=\"text-gray-100\">Portfolio</a>\n              <a href=\"/contact\" className=\"text-gray-300 hover:text-gray-100 transition-colors\">Contact</a>\n            </nav>\n            <button className=\"bg-gradient-to-r from-gray-600 to-gray-800 text-white px-6 py-2 rounded-lg font-semibold hover:shadow-lg transition-all\">\n              Get Quote\n            </button>\n          </div>\n        </div>\n      </header>\n\n      <main className=\"pt-20\">\n        {/* Hero Section */}\n        <section className=\"py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-16\">\n              <h1 className=\"text-5xl md:text-6xl font-bold mb-6\">\n                Our <span className=\"bg-gradient-to-r from-gray-300 to-white bg-clip-text text-transparent\">Portfolio</span>\n              </h1>\n              <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n                Discover our award-winning projects that have captivated audiences and elevated brands \n                to new heights of visual excellence.\n              </p>\n            </div>\n          </div>\n        </section>\n\n        {/* Filter Tabs */}\n        <section className=\"py-8 bg-gray-800 sticky top-20 z-40\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex flex-wrap justify-center gap-4\">\n              {filters.map((filter) => (\n                <button\n                  key={filter}\n                  onClick={() => setActiveFilter(filter)}\n                  className={`px-6 py-2 rounded-full font-semibold transition-all ${\n                    activeFilter === filter\n                      ? 'bg-gradient-to-r from-gray-600 to-gray-800 text-white'\n                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'\n                  }`}\n                >\n                  {filter}\n                </button>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Portfolio Grid */}\n        <section className=\"py-20 bg-gray-900\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {filteredProjects.map((project) => (\n                <div key={project.id} className=\"group cursor-pointer\">\n                  <div className=\"bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-lg overflow-hidden hover:border-yellow-500/30 hover:shadow-lg transition-all\">\n                    {/* Project Thumbnail */}\n                    <div className=\"relative aspect-video bg-gradient-to-br from-gray-700 to-gray-600 overflow-hidden\">\n                      <div className=\"absolute inset-0 flex items-center justify-center\">\n                        <div className=\"text-center text-yellow-500/40\">\n                          <div className=\"text-4xl mb-2\">🎬</div>\n                          <p className=\"text-sm\">Project Thumbnail</p>\n                        </div>\n                      </div>\n                      \n                      {/* Overlay */}\n                      <div className=\"absolute inset-0 bg-gray-900/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center\">\n                        <div className=\"flex space-x-4\">\n                          <button className=\"w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center text-gray-900 hover:bg-yellow-400 transition-colors\">\n                            ▶\n                          </button>\n                          <button className=\"w-12 h-12 bg-gray-100/20 backdrop-blur-sm rounded-full flex items-center justify-center text-gray-100 hover:bg-gray-100/30 transition-colors\">\n                            🔗\n                          </button>\n                        </div>\n                      </div>\n\n                      {/* Category Badge */}\n                      <div className=\"absolute top-4 left-4\">\n                        <span className=\"px-3 py-1 bg-yellow-500/90 text-gray-900 text-xs font-semibold rounded-full uppercase tracking-wide\">\n                          {project.category}\n                        </span>\n                      </div>\n\n                      {/* Year Badge */}\n                      <div className=\"absolute top-4 right-4\">\n                        <span className=\"px-3 py-1 bg-gray-900/80 text-gray-100 text-xs font-semibold rounded-full\">\n                          {project.year}\n                        </span>\n                      </div>\n                    </div>\n\n                    {/* Project Info */}\n                    <div className=\"p-6\">\n                      <h3 className=\"text-xl font-semibold mb-2 group-hover:text-yellow-500 transition-colors\">\n                        {project.title}\n                      </h3>\n                      <p className=\"text-yellow-400 text-sm font-medium mb-3 uppercase tracking-wide\">\n                        {project.client}\n                      </p>\n                      <p className=\"text-gray-300 text-sm leading-relaxed mb-4\">\n                        {project.description}\n                      </p>\n\n                      {/* Tags */}\n                      <div className=\"flex flex-wrap gap-2 mb-4\">\n                        {project.tags.map((tag, index) => (\n                          <span key={index} className=\"px-2 py-1 bg-gray-700/50 text-gray-400 text-xs rounded\">\n                            {tag}\n                          </span>\n                        ))}\n                      </div>\n\n                      {/* Awards */}\n                      {project.awards.length > 0 && (\n                        <div className=\"space-y-1\">\n                          {project.awards.map((award, index) => (\n                            <div key={index} className=\"flex items-center text-xs text-gray-400\">\n                              <span className=\"text-yellow-500 mr-2\">🏆</span>\n                              {award}\n                            </div>\n                          ))}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {filteredProjects.length === 0 && (\n              <div className=\"text-center py-16\">\n                <p className=\"text-gray-400 text-lg\">No projects found for the selected category.</p>\n              </div>\n            )}\n          </div>\n        </section>\n\n        {/* CTA Section */}\n        <section className=\"py-20 bg-gray-800\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n            <h2 className=\"text-4xl font-bold mb-6\">\n              Ready to Create Your <span className=\"bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent\">Masterpiece?</span>\n            </h2>\n            <p className=\"text-xl text-gray-300 mb-8 max-w-3xl mx-auto\">\n              Let's collaborate to bring your vision to life with the same excellence and creativity.\n            </p>\n            <button className=\"bg-gradient-to-r from-yellow-400 to-yellow-600 text-gray-900 px-8 py-4 rounded-lg font-semibold text-lg hover:shadow-lg transition-all\">\n              Start Your Project\n            </button>\n          </div>\n        </section>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-900 border-t border-yellow-500/20 py-12\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent mb-4\">\n              Jibreel Media\n            </div>\n            <p className=\"text-gray-400 mb-8\">\n              Crafting visual masterpieces for luxury brands and visionary clients.\n            </p>\n            <p className=\"text-gray-500 text-sm\">\n              © 2024 Jibreel Media. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,UAAU;QAAC;QAAO;QAAc;QAAe;QAAe;QAAa;KAAS;IAE1F,MAAM,WAAW;QACf;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,UAAU;YACV,aAAa;YACb,MAAM;YACN,QAAQ;gBAAC;gBAAwB;aAA4B;YAC7D,MAAM;gBAAC;gBAAW;gBAAU;aAAY;QAC1C;QACA;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,UAAU;YACV,aAAa;YACb,MAAM;YACN,QAAQ;gBAAC;aAAyB;YAClC,MAAM;gBAAC;gBAAa;gBAAc;aAAU;QAC9C;QACA;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,UAAU;YACV,aAAa;YACb,MAAM;YACN,QAAQ;gBAAC;gBAAoB;aAAuB;YACpD,MAAM;gBAAC;gBAAS;gBAAW;aAAW;QACxC;QACA;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,UAAU;YACV,aAAa;YACb,MAAM;YACN,QAAQ;gBAAC;aAAyB;YAClC,MAAM;gBAAC;gBAAW;gBAAU;aAAgB;QAC9C;QACA;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,UAAU;YACV,aAAa;YACb,MAAM;YACN,QAAQ;gBAAC;aAAmC;YAC5C,MAAM;gBAAC;gBAAS;gBAAgB;aAAU;QAC5C;QACA;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,UAAU;YACV,aAAa;YACb,MAAM;YACN,QAAQ,EAAE;YACV,MAAM;gBAAC;gBAAS;gBAAc;aAAa;QAC7C;KACD;IAED,MAAM,mBAAmB,iBAAiB,QACtC,WACA,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAEpD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,MAAK;gCAAI,WAAU;0CAA2F;;;;;;0CAGjH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAsD;;;;;;kDAC5E,6LAAC;wCAAE,MAAK;wCAAS,WAAU;kDAAsD;;;;;;kDACjF,6LAAC;wCAAE,MAAK;wCAAY,WAAU;kDAAsD;;;;;;kDACpF,6LAAC;wCAAE,MAAK;wCAAa,WAAU;kDAAgB;;;;;;kDAC/C,6LAAC;wCAAE,MAAK;wCAAW,WAAU;kDAAsD;;;;;;;;;;;;0CAErF,6LAAC;gCAAO,WAAU;0CAA0H;;;;;;;;;;;;;;;;;;;;;;0BAOlJ,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;4CAAsC;0DAC9C,6LAAC;gDAAK,WAAU;0DAAwE;;;;;;;;;;;;kDAE9F,6LAAC;wCAAE,WAAU;kDAA0C;;;;;;;;;;;;;;;;;;;;;;kCAS7D,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;wCAEC,SAAS,IAAM,gBAAgB;wCAC/B,WAAW,AAAC,uDAIX,OAHC,iBAAiB,SACb,0DACA;kDAGL;uCARI;;;;;;;;;;;;;;;;;;;;kCAgBf,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC;4CAAqB,WAAU;sDAC9B,cAAA,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFAAgB;;;;;;sFAC/B,6LAAC;4EAAE,WAAU;sFAAU;;;;;;;;;;;;;;;;;0EAK3B,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAO,WAAU;sFAA4H;;;;;;sFAG9I,6LAAC;4EAAO,WAAU;sFAA+I;;;;;;;;;;;;;;;;;0EAOrK,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EACb,QAAQ,QAAQ;;;;;;;;;;;0EAKrB,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EACb,QAAQ,IAAI;;;;;;;;;;;;;;;;;kEAMnB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EACX,QAAQ,KAAK;;;;;;0EAEhB,6LAAC;gEAAE,WAAU;0EACV,QAAQ,MAAM;;;;;;0EAEjB,6LAAC;gEAAE,WAAU;0EACV,QAAQ,WAAW;;;;;;0EAItB,6LAAC;gEAAI,WAAU;0EACZ,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACtB,6LAAC;wEAAiB,WAAU;kFACzB;uEADQ;;;;;;;;;;4DAOd,QAAQ,MAAM,CAAC,MAAM,GAAG,mBACvB,6LAAC;gEAAI,WAAU;0EACZ,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC1B,6LAAC;wEAAgB,WAAU;;0FACzB,6LAAC;gFAAK,WAAU;0FAAuB;;;;;;4EACtC;;uEAFO;;;;;;;;;;;;;;;;;;;;;;2CA/DZ,QAAQ,EAAE;;;;;;;;;;gCA4EvB,iBAAiB,MAAM,KAAK,mBAC3B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;kCAO7C,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCAA0B;sDACjB,6LAAC;4CAAK,WAAU;sDAA+E;;;;;;;;;;;;8CAEtH,6LAAC;oCAAE,WAAU;8CAA+C;;;;;;8CAG5D,6LAAC;oCAAO,WAAU;8CAAyI;;;;;;;;;;;;;;;;;;;;;;;0BAQjK,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAuG;;;;;;0CAGtH,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;GA/PwB;KAAA", "debugId": null}}, {"offset": {"line": 658, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/GitHub/Jibreel_Media/jibreel-media/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,KAAK,WAAW,IAAI;YAC7B,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,0BAA0B,SAAU,iBAAiB;YACnD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,MAAM,wBAAwB,CAAC,IAAI,CAC9D,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 865, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/GitHub/Jibreel_Media/jibreel-media/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}