{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/GitHub/Jibreel_Media/jibreel-media/src/components/ui/Logo.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Image from 'next/image';\nimport { Video } from 'lucide-react';\n\ninterface LogoProps {\n  className?: string;\n  size?: 'sm' | 'md' | 'lg';\n  showIcon?: boolean;\n  textOnly?: boolean;\n}\n\nconst Logo: React.FC<LogoProps> = ({\n  className = '',\n  size = 'md',\n  showIcon = true,\n  textOnly = false\n}) => {\n  const [imageError, setImageError] = useState(false);\n\n  const sizeClasses = {\n    sm: 'text-lg',\n    md: 'text-2xl',\n    lg: 'text-4xl'\n  };\n\n  const logoSizes = {\n    sm: { width: 120, height: 40 },\n    md: { width: 180, height: 60 },\n    lg: { width: 240, height: 80 }\n  };\n\n  const iconSizes = {\n    sm: 'w-5 h-5',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  };\n\n  // If textOnly is true, just show the text\n  if (textOnly) {\n    return (\n      <div className={`font-ethnocentric ${sizeClasses[size]} bg-gradient-to-r from-gray-300 to-white bg-clip-text text-transparent tracking-wider ${className}`}>\n        JIBREEL MEDIA\n      </div>\n    );\n  }\n\n  // Try to use the logo image first, fallback to icon + text\n  return (\n    <div className={`flex items-center ${className}`}>\n      {!imageError ? (\n        <Image\n          src=\"/images/jibreel-logo.svg\"\n          alt=\"Jibreel Media\"\n          width={logoSizes[size].width}\n          height={logoSizes[size].height}\n          className=\"object-contain\"\n          onError={() => setImageError(true)}\n          priority\n        />\n      ) : (\n        // Fallback to icon + text if image fails to load\n        <div className=\"flex items-center space-x-3\">\n          {showIcon && (\n            <div className=\"relative\">\n              {/* Film camera icon - more detailed */}\n              <div className=\"relative bg-black rounded-lg p-1\">\n                <Video className={`${iconSizes[size]} text-white`} />\n                {/* Film reels on top */}\n                <div className=\"absolute -top-2 left-1\">\n                  <div className=\"w-4 h-4 bg-gray-800 rounded-full border-2 border-gray-600 flex items-center justify-center\">\n                    <div className=\"w-1 h-1 bg-gray-400 rounded-full\"></div>\n                  </div>\n                </div>\n                <div className=\"absolute -top-2 right-1\">\n                  <div className=\"w-4 h-4 bg-gray-800 rounded-full border-2 border-gray-600 flex items-center justify-center\">\n                    <div className=\"w-1 h-1 bg-gray-400 rounded-full\"></div>\n                  </div>\n                </div>\n                {/* Lens */}\n                <div className=\"absolute -right-2 top-1/2 transform -translate-y-1/2\">\n                  <div className=\"w-3 h-3 bg-gray-700 rounded-full border border-gray-500\"></div>\n                </div>\n              </div>\n            </div>\n          )}\n          <div className={`font-ethnocentric ${sizeClasses[size]} bg-gradient-to-r from-gray-300 to-white bg-clip-text text-transparent tracking-wider`}>\n            JIBREEL MEDIA\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Logo;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAaA,MAAM,OAA4B;QAAC,EACjC,YAAY,EAAE,EACd,OAAO,IAAI,EACX,WAAW,IAAI,EACf,WAAW,KAAK,EACjB;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,YAAY;QAChB,IAAI;YAAE,OAAO;YAAK,QAAQ;QAAG;QAC7B,IAAI;YAAE,OAAO;YAAK,QAAQ;QAAG;QAC7B,IAAI;YAAE,OAAO;YAAK,QAAQ;QAAG;IAC/B;IAEA,MAAM,YAAY;QAChB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,0CAA0C;IAC1C,IAAI,UAAU;QACZ,qBACE,6LAAC;YAAI,WAAW,AAAC,qBAA8H,OAA1G,WAAW,CAAC,KAAK,EAAC,0FAAkG,OAAV;sBAAa;;;;;;IAIhK;IAEA,2DAA2D;IAC3D,qBACE,6LAAC;QAAI,WAAW,AAAC,qBAA8B,OAAV;kBAClC,CAAC,2BACA,6LAAC,gIAAA,CAAA,UAAK;YACJ,KAAI;YACJ,KAAI;YACJ,OAAO,SAAS,CAAC,KAAK,CAAC,KAAK;YAC5B,QAAQ,SAAS,CAAC,KAAK,CAAC,MAAM;YAC9B,WAAU;YACV,SAAS,IAAM,cAAc;YAC7B,QAAQ;;;;;uDAGV,iDAAiD;sBACjD,6LAAC;YAAI,WAAU;;gBACZ,0BACC,6LAAC;oBAAI,WAAU;8BAEb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAW,AAAC,GAAkB,OAAhB,SAAS,CAAC,KAAK,EAAC;;;;;;0CAErC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;0CAGnB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;0CAInB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;8BAKvB,6LAAC;oBAAI,WAAW,AAAC,qBAAsC,OAAlB,WAAW,CAAC,KAAK,EAAC;8BAAwF;;;;;;;;;;;;;;;;;AAOzJ;GAjFM;KAAA;uCAmFS", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/GitHub/Jibreel_Media/jibreel-media/src/app/portfolio/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Logo from '@/components/ui/Logo';\nimport { Play, Award, Calendar, Tag, ArrowRight, Eye } from 'lucide-react';\n\nexport default function Portfolio() {\n  const [activeFilter, setActiveFilter] = useState('All');\n\n  const filters = ['All', 'Commercial', 'Documentary', 'Music Video', 'Corporate', 'Luxury'];\n\n  const projects = [\n    {\n      id: 1,\n      title: 'Luxury Fashion Campaign',\n      client: 'Premium Fashion House',\n      category: 'Commercial',\n      description: 'A cinematic brand campaign showcasing elegance and sophistication through masterful editing and color grading.',\n      year: '2024',\n      awards: ['Best Commercial 2024', 'Creative Excellence Award'],\n      tags: ['Fashion', 'Luxury', 'Cinematic'],\n      color: 'blue',\n      featured: true\n    },\n    {\n      id: 2,\n      title: 'Corporate Innovation Story',\n      client: 'Fortune 500 Company',\n      category: 'Corporate',\n      description: 'An inspiring corporate documentary highlighting innovation and company culture.',\n      year: '2024',\n      awards: ['Documentary Excellence'],\n      tags: ['Corporate', 'Innovation', 'Culture'],\n      color: 'green',\n      featured: false\n    },\n    {\n      id: 3,\n      title: 'Midnight Bloom',\n      client: 'International Artist',\n      category: 'Music Video',\n      description: 'Dynamic visual storytelling synchronized with rhythm, featuring advanced motion graphics.',\n      year: '2023',\n      awards: ['Best Music Video', 'Visual Effects Award'],\n      tags: ['Music', 'Effects', 'Artistic'],\n      color: 'purple',\n      featured: true\n    },\n    {\n      id: 4,\n      title: 'Heritage Collection',\n      client: 'Luxury Watch Brand',\n      category: 'Luxury',\n      description: 'Elegant product showcase emphasizing craftsmanship and timeless design.',\n      year: '2023',\n      awards: ['Luxury Marketing Award'],\n      tags: ['Product', 'Luxury', 'Craftsmanship'],\n      color: 'orange',\n      featured: false\n    },\n    {\n      id: 5,\n      title: 'Urban Echoes',\n      client: 'Independent Film',\n      category: 'Documentary',\n      description: 'A compelling narrative exploring urban landscapes and hidden stories.',\n      year: '2023',\n      awards: ['Documentary Film Festival Winner'],\n      tags: ['Urban', 'Storytelling', 'Culture'],\n      color: 'indigo',\n      featured: true\n    },\n    {\n      id: 6,\n      title: 'Tech Summit Highlights',\n      client: 'Technology Conference',\n      category: 'Corporate',\n      description: 'Fast-paced event coverage capturing key moments and insights.',\n      year: '2024',\n      awards: [],\n      tags: ['Event', 'Technology', 'Conference'],\n      color: 'pink',\n      featured: false\n    }\n  ];\n\n  const filteredProjects = activeFilter === 'All'\n    ? projects\n    : projects.filter(project => project.category === activeFilter);\n\n  return (\n    <div className=\"min-h-screen bg-white text-black\">\n      {/* Apple-style Header */}\n      <header className=\"fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200/50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-12\">\n            <a href=\"/\">\n              <Logo size=\"sm\" />\n            </a>\n            <nav className=\"hidden md:flex items-center space-x-8\">\n              <a href=\"/\" className=\"text-gray-600 hover:text-black transition-colors text-sm font-medium\">Home</a>\n              <a href=\"/about\" className=\"text-gray-600 hover:text-black transition-colors text-sm font-medium\">About</a>\n              <a href=\"/services\" className=\"text-gray-600 hover:text-black transition-colors text-sm font-medium\">Services</a>\n              <a href=\"/portfolio\" className=\"text-black text-sm font-medium\">Portfolio</a>\n              <a href=\"/contact\" className=\"text-gray-600 hover:text-black transition-colors text-sm font-medium\">Contact</a>\n            </nav>\n            <button className=\"bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-medium hover:bg-blue-700 transition-all\">\n              Get Quote\n            </button>\n          </div>\n        </div>\n      </header>\n\n\n\n      {/* Sub Navigation */}\n      <div className=\"fixed top-12 left-0 right-0 z-40 bg-white/90 backdrop-blur-md border-b border-gray-200/50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-center space-x-8 h-12 overflow-x-auto\">\n            {filters.map((filter) => (\n              <button\n                key={filter}\n                onClick={() => setActiveFilter(filter)}\n                className={`text-sm font-medium whitespace-nowrap transition-colors ${\n                  activeFilter === filter\n                    ? 'text-blue-600'\n                    : 'text-gray-600 hover:text-black'\n                }`}\n              >\n                {filter}\n              </button>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      <main className=\"pt-24\">\n        {/* Hero Section - Apple Style */}\n        <section className=\"bg-black text-white py-20\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n            <h1 className=\"text-6xl md:text-8xl font-bold mb-4 tracking-tight\">\n              Portfolio\n            </h1>\n            <p className=\"text-2xl md:text-3xl font-light mb-8 text-gray-300\">\n              Designed to inspire.\n            </p>\n\n            {/* Large Visual */}\n            <div className=\"relative max-w-4xl mx-auto mb-12\">\n              <div className=\"aspect-video bg-gradient-to-br from-gray-800 to-gray-900 rounded-3xl flex items-center justify-center\">\n                <div className=\"text-center\">\n                  <Eye className=\"w-24 h-24 mx-auto mb-4 text-gray-400\" />\n                  <p className=\"text-gray-400 text-lg\">Award-Winning Projects</p>\n                </div>\n              </div>\n            </div>\n\n            {/* Stats */}\n            <div className=\"grid grid-cols-3 gap-8 max-w-2xl mx-auto text-center\">\n              <div>\n                <div className=\"text-3xl font-bold text-blue-400\">50+</div>\n                <div className=\"text-sm text-gray-400 uppercase tracking-wide\">Projects</div>\n              </div>\n              <div>\n                <div className=\"text-3xl font-bold text-blue-400\">25+</div>\n                <div className=\"text-sm text-gray-400 uppercase tracking-wide\">Awards</div>\n              </div>\n              <div>\n                <div className=\"text-3xl font-bold text-blue-400\">100%</div>\n                <div className=\"text-sm text-gray-400 uppercase tracking-wide\">Satisfaction</div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n\n\n        {/* Featured Projects - Apple Style */}\n        {filteredProjects.filter(project => project.featured).map((project, index) => {\n          const isEven = index % 2 === 0;\n          const colorClasses = {\n            blue: { bg: 'from-blue-100 to-blue-200', text: 'text-blue-600', button: 'bg-blue-600 hover:bg-blue-700' },\n            purple: { bg: 'from-purple-100 to-purple-200', text: 'text-purple-600', button: 'bg-purple-600 hover:bg-purple-700' },\n            green: { bg: 'from-green-100 to-green-200', text: 'text-green-600', button: 'bg-green-600 hover:bg-green-700' },\n            orange: { bg: 'from-orange-100 to-orange-200', text: 'text-orange-600', button: 'bg-orange-600 hover:bg-orange-700' },\n            pink: { bg: 'from-pink-100 to-pink-200', text: 'text-pink-600', button: 'bg-pink-600 hover:bg-pink-700' },\n            indigo: { bg: 'from-indigo-100 to-indigo-200', text: 'text-indigo-600', button: 'bg-indigo-600 hover:bg-indigo-700' }\n          };\n          const colors = colorClasses[project.color];\n\n          return (\n            <section key={project.id} className={`${isEven ? 'bg-gray-50' : 'bg-white'} py-20`}>\n              <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n                <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\">\n                  <div className={`${isEven ? 'order-2 lg:order-1' : 'order-1'}`}>\n                    <div className=\"flex items-center space-x-2 mb-4\">\n                      <span className={`px-3 py-1 ${colors.button} text-white text-sm font-medium rounded-full`}>\n                        {project.category}\n                      </span>\n                      <span className=\"text-gray-600 text-sm\">{project.year}</span>\n                    </div>\n                    <h2 className=\"text-5xl md:text-6xl font-bold mb-2 text-black\">\n                      {project.title}\n                    </h2>\n                    <p className={`text-xl ${colors.text} mb-6 font-medium`}>\n                      {project.client}\n                    </p>\n                    <p className=\"text-xl text-gray-600 mb-8\">\n                      {project.description}\n                    </p>\n\n                    {/* Tags */}\n                    <div className=\"flex flex-wrap gap-4 mb-8\">\n                      {project.tags.map((tag, tagIndex) => (\n                        <span key={tagIndex} className=\"flex items-center space-x-2 text-gray-700\">\n                          <Tag className=\"w-4 h-4\" />\n                          <span>{tag}</span>\n                        </span>\n                      ))}\n                    </div>\n\n                    {/* Awards */}\n                    {project.awards.length > 0 && (\n                      <div className=\"mb-8\">\n                        <h4 className=\"text-lg font-semibold text-black mb-4 flex items-center space-x-2\">\n                          <Award className={`w-5 h-5 ${colors.text}`} />\n                          <span>Awards</span>\n                        </h4>\n                        <div className=\"space-y-2\">\n                          {project.awards.map((award, awardIndex) => (\n                            <div key={awardIndex} className=\"flex items-center space-x-3\">\n                              <div className={`w-2 h-2 ${colors.button} rounded-full`}></div>\n                              <span className=\"text-gray-700\">{award}</span>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    )}\n\n                    <div className=\"flex flex-col sm:flex-row gap-4\">\n                      <button className={`${colors.button} text-white px-6 py-3 rounded-full font-medium transition-all flex items-center space-x-2`}>\n                        <Play className=\"w-4 h-4\" />\n                        <span>Watch project</span>\n                      </button>\n                      <button className=\"text-gray-600 font-medium flex items-center space-x-2 hover:underline\">\n                        <span>View case study</span>\n                        <ArrowRight className=\"w-4 h-4\" />\n                      </button>\n                    </div>\n                  </div>\n                  <div className={`${isEven ? 'order-1 lg:order-2' : 'order-2'}`}>\n                    <div className={`aspect-video bg-gradient-to-br ${colors.bg} rounded-3xl flex items-center justify-center relative overflow-hidden`}>\n                      <div className=\"text-center\">\n                        <Play className={`w-24 h-24 ${colors.text} mb-4`} />\n                        <p className={`${colors.text} text-lg font-medium`}>Project Preview</p>\n                      </div>\n                      {/* Play button overlay */}\n                      <div className=\"absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-black/20 rounded-3xl\">\n                        <button className=\"w-16 h-16 bg-white/90 rounded-full flex items-center justify-center\">\n                          <Play className=\"w-6 h-6 text-black ml-1\" />\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </section>\n          );\n        })}\n\n        {/* All Projects Grid */}\n        <section className=\"bg-gray-50 py-20\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-5xl md:text-6xl font-bold mb-6 text-black\">\n                All Projects\n              </h2>\n              <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n                Explore our complete portfolio of award-winning projects.\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {filteredProjects.map((project) => {\n                const colorClasses = {\n                  blue: 'from-blue-100 to-blue-200 text-blue-600',\n                  purple: 'from-purple-100 to-purple-200 text-purple-600',\n                  green: 'from-green-100 to-green-200 text-green-600',\n                  orange: 'from-orange-100 to-orange-200 text-orange-600',\n                  pink: 'from-pink-100 to-pink-200 text-pink-600',\n                  indigo: 'from-indigo-100 to-indigo-200 text-indigo-600'\n                };\n                const colors = colorClasses[project.color];\n\n                return (\n                  <div key={project.id} className=\"bg-white rounded-3xl p-6 shadow-lg hover:shadow-xl transition-shadow group cursor-pointer\">\n                    {/* Project Thumbnail */}\n                    <div className={`aspect-video bg-gradient-to-br ${colors} rounded-2xl mb-6 flex items-center justify-center relative overflow-hidden`}>\n                      <Play className=\"w-12 h-12\" />\n                      {/* Hover overlay */}\n                      <div className=\"absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center\">\n                        <button className=\"w-12 h-12 bg-white/90 rounded-full flex items-center justify-center\">\n                          <Play className=\"w-4 h-4 text-black ml-0.5\" />\n                        </button>\n                      </div>\n                    </div>\n\n                    {/* Project Info */}\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <span className=\"px-3 py-1 bg-gray-100 text-gray-700 text-sm font-medium rounded-full\">\n                        {project.category}\n                      </span>\n                      <div className=\"flex items-center space-x-1 text-gray-500\">\n                        <Calendar className=\"w-4 h-4\" />\n                        <span className=\"text-sm\">{project.year}</span>\n                      </div>\n                    </div>\n\n                    <h3 className=\"text-xl font-bold mb-2 text-black group-hover:text-blue-600 transition-colors\">\n                      {project.title}\n                    </h3>\n                    <p className=\"text-gray-600 text-sm mb-4\">{project.client}</p>\n                    <p className=\"text-gray-700 text-sm mb-4 leading-relaxed\">\n                      {project.description}\n                    </p>\n\n                    {/* Awards */}\n                    {project.awards.length > 0 && (\n                      <div className=\"border-t border-gray-200 pt-4\">\n                        <div className=\"flex items-center space-x-2 mb-2\">\n                          <Award className=\"w-4 h-4 text-yellow-500\" />\n                          <span className=\"text-sm font-medium text-gray-700\">Awards</span>\n                        </div>\n                        <div className=\"space-y-1\">\n                          {project.awards.slice(0, 2).map((award, awardIndex) => (\n                            <div key={awardIndex} className=\"text-xs text-gray-600\">\n                              {award}\n                            </div>\n                          ))}\n                          {project.awards.length > 2 && (\n                            <div className=\"text-xs text-blue-600\">\n                              +{project.awards.length - 2} more\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                );\n              })}\n            </div>\n\n            {filteredProjects.length === 0 && (\n              <div className=\"text-center py-16\">\n                <p className=\"text-gray-600 text-lg\">No projects found for the selected category.</p>\n              </div>\n            )}\n          </div>\n        </section>\n\n        {/* CTA Section */}\n        <section className=\"bg-white py-20\">\n          <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n            <h2 className=\"text-5xl md:text-6xl font-bold mb-6 text-black\">\n              Ready to create your masterpiece?\n            </h2>\n            <p className=\"text-xl text-gray-600 mb-12\">\n              Let's collaborate to bring your vision to life with the same excellence and creativity.\n            </p>\n\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n              <button className=\"bg-blue-600 text-white px-8 py-3 rounded-full text-lg font-medium hover:bg-blue-700 transition-all\">\n                Start your project\n              </button>\n              <button className=\"text-blue-600 font-medium flex items-center space-x-2 hover:underline\">\n                <span>View our process</span>\n                <ArrowRight className=\"w-4 h-4\" />\n              </button>\n            </div>\n          </div>\n        </section>\n      </main>\n\n      {/* Apple-style Footer */}\n      <footer className=\"bg-gray-100 py-12\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          {/* Footer Links */}\n          <div className=\"grid grid-cols-2 md:grid-cols-5 gap-8 mb-8\">\n            <div>\n              <h4 className=\"text-sm font-semibold text-black mb-4\">Services</h4>\n              <ul className=\"space-y-2 text-sm text-gray-600\">\n                <li><a href=\"#\" className=\"hover:text-black transition-colors\">Video Editing</a></li>\n                <li><a href=\"#\" className=\"hover:text-black transition-colors\">Motion Graphics</a></li>\n                <li><a href=\"#\" className=\"hover:text-black transition-colors\">Color Grading</a></li>\n                <li><a href=\"#\" className=\"hover:text-black transition-colors\">Audio Production</a></li>\n              </ul>\n            </div>\n\n            <div>\n              <h4 className=\"text-sm font-semibold text-black mb-4\">Portfolio</h4>\n              <ul className=\"space-y-2 text-sm text-gray-600\">\n                <li><a href=\"#\" className=\"hover:text-black transition-colors\">Commercial</a></li>\n                <li><a href=\"#\" className=\"hover:text-black transition-colors\">Corporate</a></li>\n                <li><a href=\"#\" className=\"hover:text-black transition-colors\">Music Videos</a></li>\n                <li><a href=\"#\" className=\"hover:text-black transition-colors\">Documentaries</a></li>\n              </ul>\n            </div>\n\n            <div>\n              <h4 className=\"text-sm font-semibold text-black mb-4\">Support</h4>\n              <ul className=\"space-y-2 text-sm text-gray-600\">\n                <li><a href=\"#\" className=\"hover:text-black transition-colors\">Contact Us</a></li>\n                <li><a href=\"#\" className=\"hover:text-black transition-colors\">Get Quote</a></li>\n                <li><a href=\"#\" className=\"hover:text-black transition-colors\">FAQ</a></li>\n                <li><a href=\"#\" className=\"hover:text-black transition-colors\">Resources</a></li>\n              </ul>\n            </div>\n\n            <div>\n              <h4 className=\"text-sm font-semibold text-black mb-4\">Company</h4>\n              <ul className=\"space-y-2 text-sm text-gray-600\">\n                <li><a href=\"#\" className=\"hover:text-black transition-colors\">About</a></li>\n                <li><a href=\"#\" className=\"hover:text-black transition-colors\">Careers</a></li>\n                <li><a href=\"#\" className=\"hover:text-black transition-colors\">Press</a></li>\n                <li><a href=\"#\" className=\"hover:text-black transition-colors\">Blog</a></li>\n              </ul>\n            </div>\n\n            <div>\n              <h4 className=\"text-sm font-semibold text-black mb-4\">Connect</h4>\n              <ul className=\"space-y-2 text-sm text-gray-600\">\n                <li><a href=\"#\" className=\"hover:text-black transition-colors\">Instagram</a></li>\n                <li><a href=\"#\" className=\"hover:text-black transition-colors\">Twitter</a></li>\n                <li><a href=\"#\" className=\"hover:text-black transition-colors\">LinkedIn</a></li>\n                <li><a href=\"#\" className=\"hover:text-black transition-colors\">YouTube</a></li>\n              </ul>\n            </div>\n          </div>\n\n          {/* Footer Bottom */}\n          <div className=\"border-t border-gray-300 pt-8\">\n            <div className=\"flex flex-col md:flex-row justify-between items-center text-sm text-gray-600\">\n              <p>Copyright © 2024 Jibreel Media Inc. All rights reserved.</p>\n              <div className=\"flex space-x-6 mt-4 md:mt-0\">\n                <a href=\"#\" className=\"hover:text-black transition-colors\">Privacy Policy</a>\n                <a href=\"#\" className=\"hover:text-black transition-colors\">Terms of Use</a>\n                <a href=\"#\" className=\"hover:text-black transition-colors\">Legal</a>\n              </div>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,UAAU;QAAC;QAAO;QAAc;QAAe;QAAe;QAAa;KAAS;IAE1F,MAAM,WAAW;QACf;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,UAAU;YACV,aAAa;YACb,MAAM;YACN,QAAQ;gBAAC;gBAAwB;aAA4B;YAC7D,MAAM;gBAAC;gBAAW;gBAAU;aAAY;YACxC,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,UAAU;YACV,aAAa;YACb,MAAM;YACN,QAAQ;gBAAC;aAAyB;YAClC,MAAM;gBAAC;gBAAa;gBAAc;aAAU;YAC5C,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,UAAU;YACV,aAAa;YACb,MAAM;YACN,QAAQ;gBAAC;gBAAoB;aAAuB;YACpD,MAAM;gBAAC;gBAAS;gBAAW;aAAW;YACtC,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,UAAU;YACV,aAAa;YACb,MAAM;YACN,QAAQ;gBAAC;aAAyB;YAClC,MAAM;gBAAC;gBAAW;gBAAU;aAAgB;YAC5C,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,UAAU;YACV,aAAa;YACb,MAAM;YACN,QAAQ;gBAAC;aAAmC;YAC5C,MAAM;gBAAC;gBAAS;gBAAgB;aAAU;YAC1C,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,UAAU;YACV,aAAa;YACb,MAAM;YACN,QAAQ,EAAE;YACV,MAAM;gBAAC;gBAAS;gBAAc;aAAa;YAC3C,OAAO;YACP,UAAU;QACZ;KACD;IAED,MAAM,mBAAmB,iBAAiB,QACtC,WACA,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAEpD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,MAAK;0CACN,cAAA,6LAAC,mIAAA,CAAA,UAAI;oCAAC,MAAK;;;;;;;;;;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAuE;;;;;;kDAC7F,6LAAC;wCAAE,MAAK;wCAAS,WAAU;kDAAuE;;;;;;kDAClG,6LAAC;wCAAE,MAAK;wCAAY,WAAU;kDAAuE;;;;;;kDACrG,6LAAC;wCAAE,MAAK;wCAAa,WAAU;kDAAiC;;;;;;kDAChE,6LAAC;wCAAE,MAAK;wCAAW,WAAU;kDAAuE;;;;;;;;;;;;0CAEtG,6LAAC;gCAAO,WAAU;0CAAqG;;;;;;;;;;;;;;;;;;;;;;0BAU7H,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;gCAEC,SAAS,IAAM,gBAAgB;gCAC/B,WAAW,AAAC,2DAIX,OAHC,iBAAiB,SACb,kBACA;0CAGL;+BARI;;;;;;;;;;;;;;;;;;;;0BAef,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAqD;;;;;;8CAGnE,6LAAC;oCAAE,WAAU;8CAAqD;;;;;;8CAKlE,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACf,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;8CAM3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAmC;;;;;;8DAClD,6LAAC;oDAAI,WAAU;8DAAgD;;;;;;;;;;;;sDAEjE,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAmC;;;;;;8DAClD,6LAAC;oDAAI,WAAU;8DAAgD;;;;;;;;;;;;sDAEjE,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAmC;;;;;;8DAClD,6LAAC;oDAAI,WAAU;8DAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAStE,iBAAiB,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,EAAE,GAAG,CAAC,CAAC,SAAS;wBAClE,MAAM,SAAS,QAAQ,MAAM;wBAC7B,MAAM,eAAe;4BACnB,MAAM;gCAAE,IAAI;gCAA6B,MAAM;gCAAiB,QAAQ;4BAAgC;4BACxG,QAAQ;gCAAE,IAAI;gCAAiC,MAAM;gCAAmB,QAAQ;4BAAoC;4BACpH,OAAO;gCAAE,IAAI;gCAA+B,MAAM;gCAAkB,QAAQ;4BAAkC;4BAC9G,QAAQ;gCAAE,IAAI;gCAAiC,MAAM;gCAAmB,QAAQ;4BAAoC;4BACpH,MAAM;gCAAE,IAAI;gCAA6B,MAAM;gCAAiB,QAAQ;4BAAgC;4BACxG,QAAQ;gCAAE,IAAI;gCAAiC,MAAM;gCAAmB,QAAQ;4BAAoC;wBACtH;wBACA,MAAM,SAAS,YAAY,CAAC,QAAQ,KAAK,CAAC;wBAE1C,qBACE,6LAAC;4BAAyB,WAAW,AAAC,GAAqC,OAAnC,SAAS,eAAe,YAAW;sCACzE,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,AAAC,GAA4C,OAA1C,SAAS,uBAAuB;;8DACjD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAW,AAAC,aAA0B,OAAd,OAAO,MAAM,EAAC;sEACzC,QAAQ,QAAQ;;;;;;sEAEnB,6LAAC;4DAAK,WAAU;sEAAyB,QAAQ,IAAI;;;;;;;;;;;;8DAEvD,6LAAC;oDAAG,WAAU;8DACX,QAAQ,KAAK;;;;;;8DAEhB,6LAAC;oDAAE,WAAW,AAAC,WAAsB,OAAZ,OAAO,IAAI,EAAC;8DAClC,QAAQ,MAAM;;;;;;8DAEjB,6LAAC;oDAAE,WAAU;8DACV,QAAQ,WAAW;;;;;;8DAItB,6LAAC;oDAAI,WAAU;8DACZ,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,yBACtB,6LAAC;4DAAoB,WAAU;;8EAC7B,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;8EACf,6LAAC;8EAAM;;;;;;;2DAFE;;;;;;;;;;gDAQd,QAAQ,MAAM,CAAC,MAAM,GAAG,mBACvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAW,AAAC,WAAsB,OAAZ,OAAO,IAAI;;;;;;8EACxC,6LAAC;8EAAK;;;;;;;;;;;;sEAER,6LAAC;4DAAI,WAAU;sEACZ,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,2BAC1B,6LAAC;oEAAqB,WAAU;;sFAC9B,6LAAC;4EAAI,WAAW,AAAC,WAAwB,OAAd,OAAO,MAAM,EAAC;;;;;;sFACzC,6LAAC;4EAAK,WAAU;sFAAiB;;;;;;;mEAFzB;;;;;;;;;;;;;;;;8DASlB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAO,WAAW,AAAC,GAAgB,OAAd,OAAO,MAAM,EAAC;;8EAClC,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;8EAAK;;;;;;;;;;;;sEAER,6LAAC;4DAAO,WAAU;;8EAChB,6LAAC;8EAAK;;;;;;8EACN,6LAAC,qNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAI5B,6LAAC;4CAAI,WAAW,AAAC,GAA4C,OAA1C,SAAS,uBAAuB;sDACjD,cAAA,6LAAC;gDAAI,WAAW,AAAC,kCAA2C,OAAV,OAAO,EAAE,EAAC;;kEAC1D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAW,AAAC,aAAwB,OAAZ,OAAO,IAAI,EAAC;;;;;;0EAC1C,6LAAC;gEAAE,WAAW,AAAC,GAAc,OAAZ,OAAO,IAAI,EAAC;0EAAuB;;;;;;;;;;;;kEAGtD,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAO,WAAU;sEAChB,cAAA,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BApEhB,QAAQ,EAAE;;;;;oBA6E5B;kCAGA,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAiD;;;;;;sDAG/D,6LAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;8CAKzD,6LAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC,CAAC;wCACrB,MAAM,eAAe;4CACnB,MAAM;4CACN,QAAQ;4CACR,OAAO;4CACP,QAAQ;4CACR,MAAM;4CACN,QAAQ;wCACV;wCACA,MAAM,SAAS,YAAY,CAAC,QAAQ,KAAK,CAAC;wCAE1C,qBACE,6LAAC;4CAAqB,WAAU;;8DAE9B,6LAAC;oDAAI,WAAW,AAAC,kCAAwC,OAAP,QAAO;;sEACvD,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAEhB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAO,WAAU;0EAChB,cAAA,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8DAMtB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEACb,QAAQ,QAAQ;;;;;;sEAEnB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;oEAAK,WAAU;8EAAW,QAAQ,IAAI;;;;;;;;;;;;;;;;;;8DAI3C,6LAAC;oDAAG,WAAU;8DACX,QAAQ,KAAK;;;;;;8DAEhB,6LAAC;oDAAE,WAAU;8DAA8B,QAAQ,MAAM;;;;;;8DACzD,6LAAC;oDAAE,WAAU;8DACV,QAAQ,WAAW;;;;;;gDAIrB,QAAQ,MAAM,CAAC,MAAM,GAAG,mBACvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;oEAAK,WAAU;8EAAoC;;;;;;;;;;;;sEAEtD,6LAAC;4DAAI,WAAU;;gEACZ,QAAQ,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,2BACtC,6LAAC;wEAAqB,WAAU;kFAC7B;uEADO;;;;;gEAIX,QAAQ,MAAM,CAAC,MAAM,GAAG,mBACvB,6LAAC;oEAAI,WAAU;;wEAAwB;wEACnC,QAAQ,MAAM,CAAC,MAAM,GAAG;wEAAE;;;;;;;;;;;;;;;;;;;;2CA9C9B,QAAQ,EAAE;;;;;oCAsDxB;;;;;;gCAGD,iBAAiB,MAAM,KAAK,mBAC3B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;kCAO7C,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAG/D,6LAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAO,WAAU;sDAAqG;;;;;;sDAGvH,6LAAC;4CAAO,WAAU;;8DAChB,6LAAC;8DAAK;;;;;;8DACN,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhC,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;8CAInE,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;8CAInE,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;8CAInE,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;8CAInE,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMrE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAE;;;;;;kDACH,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAqC;;;;;;0DAC3D,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAqC;;;;;;0DAC3D,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3E;GAhcwB;KAAA", "debugId": null}}]}